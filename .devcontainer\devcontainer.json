// For format details, see https://aka.ms/devcontainer.json. For config options, see the README at:
// https://github.com/microsoft/vscode-dev-containers/tree/v0.177.0/containers/go
{
  "name": "oh-my-posh",
  "build": {
    "dockerfile": "Dockerfile",
    "args": {
      // Update the VARIANT arg to pick a version of Go: 1, 1.16, 1.17
      // Append -bullseye or -buster to pin to an OS version.
      // Use -bullseye variants on local arm64/Apple Silicon.
      "VARIANT": "1-1.24-bullseye",

      // Override me with your own timezone:
      "TZ": "UTC",
      // Use one of the "TZ database name" entries from:
      // https://en.wikipedia.org/wiki/List_of_tz_database_time_zones

      "NODE_VERSION": "lts/*",
      //Powershell version
      "PS_VERSION": "7.2.7"
    }
  },
  "runArgs": [
    "--cap-add=SYS_PTRACE",
    "--security-opt",
    "seccomp=unconfined",
    "--security-opt",
    "label=disable"
  ],
  "containerEnv": {
    "HOME": "/home/<USER>"
  },

  "customizations": {
    "vscode": {
      "settings": {
        "go.toolsManagement.checkForUpdates": "local",
        "go.useLanguageServer": true,
        "go.gopath": "/go",
        "go.goroot": "/usr/local/go",
        "terminal.integrated.profiles.linux": {
          "bash": {
            "path": "bash"
          },
          "zsh": {
            "path": "zsh"
          },
          "fish": {
            "path": "fish"
          },
          "tmux": {
            "path": "tmux",
            "icon": "terminal-tmux"
          },
          "pwsh": {
            "path": "pwsh",
            "icon": "terminal-powershell"
          }
        },
        "terminal.integrated.defaultProfile.linux": "pwsh",
        "terminal.integrated.defaultProfile.windows": "PowerShell",
        "terminal.integrated.defaultProfile.osx": "pwsh",
        "terminal.integrated.shellIntegration.enabled": false,
        "tasks.statusbar.default.hide": true
      },
      "extensions": [
        "actboy168.tasks",
        "eamodio.gitlens",
        "tamasfe.even-better-toml",
        "davidanson.vscode-markdownlint",
        "editorconfig.editorconfig",
        "esbenp.prettier-vscode",
        "github.vscode-pull-request-github",
        "golang.go",
        "ms-vscode.powershell",
        "redhat.vscode-yaml",
        "yzhang.markdown-all-in-one",
        "ms-azuretools.vscode-azurefunctions"
      ]
    }
  },
  // Comment out connect as root instead. More info: https://aka.ms/vscode-remote/containers/non-root.
  "remoteUser": "vscode",
  // This is running the same command as the VSCode Task 'devcontainer: rebuild oh-my-posh'
  // It Compiles *oh-my-posh* from this repo while **overwriting** your preinstalled stable release.'
  // Ideal for getting straight into developing & testing whilst using a devcontainer
  "updateContentCommand": "cd src && go build -v -buildvcs=false -o /home/<USER>/bin/oh-my-posh -ldflags \"-s -w -X 'github.com/jandedobbeleer/oh-my-posh/src/build.Version=development-$(git --no-pager log -1 --pretty=%h-%s)' -extldflags '-static'\""
}
