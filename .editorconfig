; EditorConfig to support per-solution formatting.
; http://editorconfig.org/

; This is the default for the codeline.
root = true

; Default
[*]
indent_style = space
trim_trailing_whitespace = true
insert_final_newline = true
end_of_line = lf

; Go Code - match go fmt
[*.go]
indent_style = tab

; TOML - match default for dep
[*.toml]
indent_size = 2

; JavaScript and JS mixes - match eslint, other standards
[*.{js,json,ts,vue}]
indent_size = 2

; Markdown - match markdownlint settings
[*.{md,markdown}]
indent_size = 2
trim_trailing_whitespace = false

; PowerShell - match defaults for New-ModuleManifest and PSScriptAnalyzer Invoke-Formatter
[*.{ps1,psd1,psm1}]
indent_size = 4
charset = utf-8-bom

; Lua
[*.lua]
line_space_after_comment = max(2)
line_space_after_do_statement = max(2)
line_space_after_expression_statement = max(2)
line_space_after_for_statement = max(2)
line_space_after_function_statement = fixed(2)
line_space_after_if_statement = max(2)
line_space_after_local_or_assign_statement = max(2)
line_space_after_repeat_statement = max(2)
line_space_after_while_statement = max(2)
max_line_length = unset
quote_style = single
