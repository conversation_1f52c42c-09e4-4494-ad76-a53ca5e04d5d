on:
  pull_request:
    paths-ignore:
      - 'README.md'
      - 'CONTRIBUTING.md'
      - 'COPYING'
      - 'website/**'
      - '.github/*.md'
      - '.github/FUNDING.yml'

name: Validate Code
jobs:
  test:
    strategy:
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
    runs-on: ${{ matrix.os }}
    defaults:
      run:
        working-directory: ${{ github.workspace }}/src
    steps:
    - name: Checkout code
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
    - name: Install Go 🗳
      uses: ./.github/workflows/composite/bootstrap-go
    - name: Golang CI
      uses: golangci/golangci-lint-action@4afd733a84b1f43292c63897423277bb7f4313a9
      with:
        working-directory: src
    - name: Fieldalignment
      run: |
        go install golang.org/x/tools/go/analysis/passes/fieldalignment/cmd/fieldalignment@latest
        fieldalignment "./..."
    - name: Modernize
      run: |
        go install golang.org/x/tools/gopls/internal/analysis/modernize/cmd/modernize@latest
        modernize "./..."
    - name: Unit Tests
      run: go test -v ./...
