name: Release

on:
  push:
    branches:
      - main
    paths:
      - "src/**"
      - "packages/**"
      - ".github/workflows/**"
  workflow_dispatch:

jobs:
  changelog:
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.changelog.outputs.version }}
      body: ${{ steps.changelog.outputs.clean_changelog }}
      tag: ${{ steps.changelog.outputs.tag }}
      skipped: ${{ steps.changelog.outputs.skipped }}
    steps:
      - name: Checkout code 👋
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
      - name: Create changelog ✍️
        id: changelog
        uses: TriPSs/conventional-changelog-action@67139193614f5b9e8db87da1bd4240922b34d765
        with:
          github-token: ${{ secrets.github_token }}
          skip-version-file: "true"
          output-file: "false"
          skip-commit: "true"
          skip-on-empty: "true"
          skip-tag: "true"

  artifacts:
    needs: changelog
    if: ${{ needs.changelog.outputs.skipped == 'false' }}
    runs-on: windows-latest
    defaults:
      run:
        shell: pwsh
        working-directory: ${{ github.workspace }}/build
    steps:
      - name: Checkout code 👋
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
      - name: Install Go 🗳
        uses: ./.github/workflows/composite/bootstrap-go
      - name: Pre Build 😸
        env:
          SIGNING_KEY: ${{ secrets.SIGNING_KEY }}
        run: |
          ./pre.ps1 -Version ${{ needs.changelog.outputs.version }}
      - name: Run GoReleaser 🚀
        uses: goreleaser/goreleaser-action@9c156ee8a17a598857849441385a2041ef570552
        with:
          distribution: goreleaser
          version: v2.3.2
          args: release --clean --skip publish
          workdir: src
        env:
          AZURE_CLIENT_SECRET: ${{ secrets.AZURE_CLIENT_SECRET }}
          AZURE_CLIENT_ID: ${{ secrets.AZURE_CLIENT_ID }}
          AZURE_TENANT_ID: ${{ secrets.AZURE_TENANT_ID }}
      - name: Post Build 🤐
        run: |
          ./post.ps1
      - uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02
        with:
          name: build-artifacts
          path: |
            src/dist/posh-*
            src/dist/themes.*
            src/dist/checksums.*
  msi:
    needs:
      - changelog
      - artifacts
    runs-on: windows-latest
    strategy:
      matrix:
        arch: [x64, arm64, x86]
    defaults:
      run:
        shell: pwsh
        working-directory: ${{ github.workspace }}/packages/msi
    steps:
      - name: Checkout code 👋
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
      - uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
        with:
          name: build-artifacts
          path: dist
      - name: Install Wix Toolset 🛠
        run: dotnet tool install --global wix
      - name: Build installer 📦
        id: build
        env:
          AZURE_CLIENT_SECRET: ${{ secrets.AZURE_CLIENT_SECRET }}
          AZURE_CLIENT_ID: ${{ secrets.AZURE_CLIENT_ID }}
          AZURE_TENANT_ID: ${{ secrets.AZURE_TENANT_ID }}
        run: |
          $version = '${{ needs.changelog.outputs.version }}'.TrimStart("v")
          ./build.ps1 -Architecture ${{ matrix.arch }}  -Version $version -Copy -Sign
      - uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02
        with:
          name: msi-artifact-${{ matrix.arch }}
          path: |
            packages/msi/out/install-${{ matrix.arch }}.msi
            packages/msi/out/install-${{ matrix.arch }}.msi.sha256
            packages/msi/out/install-${{ matrix.arch }}.msix
            packages/msi/out/install-${{ matrix.arch }}.msix.sha256
  release:
    runs-on: ubuntu-latest
    needs:
      - changelog
      - artifacts
      - msi
    steps:
      - uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093
        with:
          merge-multiple: true
      - name: Upload version file
        env:
          AZURE_STORAGE_CONNECTION_STRING: ${{ secrets.CDN_CONNECTIONSTRING }}
        run: |
          echo v${{ needs.changelog.outputs.version }} > version.txt
          az storage blob upload-batch --destination v${{ needs.changelog.outputs.version }} --source .
          az storage blob upload-batch --destination latest --overwrite true --source .
      - name: Release 🎓
        uses: softprops/action-gh-release@72f2c25fcb47643c292f7107632f7a47c1df5cd8
        with:
          tag_name: ${{ needs.changelog.outputs.tag }}
          body: ${{ needs.changelog.outputs.body }}
          fail_on_unmatched_files: true
          token: ${{ secrets.GH_PAT }}
          files: |
            *
