name: Winget
on:
  release:
    types: [published]

jobs:
  publish:
    runs-on: windows-latest
    defaults:
      run:
        shell: pwsh
        working-directory: ${{ github.workspace }}/packages/winget
    env:
      WINGETCREATE_TOKEN: ${{ secrets.WINGETCREATE_TOKEN }}
    steps:
    - name: Checkout code 👋
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
    - name: Create manifest and submit PR 📦
      run: |
        ./build.ps1 -Version "${{ github.event.release.tag_name }}" -Token $env:WINGETCREATE_TOKEN
