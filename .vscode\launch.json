{"version": "0.2.0", "configurations": [{"name": "Primary", "type": "go", "request": "launch", "mode": "debug", "program": "${workspaceRoot}/src", "args": ["prompt", "print", "primary", "--shell=pwsh", "--terminal-width=200"]}, {"name": "<PERSON><PERSON><PERSON>", "type": "go", "request": "launch", "mode": "debug", "program": "${workspaceRoot}/src", "args": ["prompt", "print", "tooltip", "--command=git", "--shell=pwsh"]}, {"name": "Transient", "type": "go", "request": "launch", "mode": "debug", "program": "${workspaceRoot}/src", "args": ["prompt", "print", "transient", "--shell=pwsh", "--status=1"]}, {"name": "Launch tests", "type": "go", "request": "launch", "mode": "test", "program": "${workspaceRoot}/src", "args": ["--test.v"]}, {"name": "Debug", "type": "go", "request": "launch", "mode": "debug", "program": "${workspaceRoot}/src", "args": ["prompt", "debug"]}, {"name": "Init", "type": "go", "request": "launch", "mode": "debug", "program": "${workspaceRoot}/src", "args": ["prompt", "init", "cmd", "--print"]}, {"name": "Export Config", "type": "go", "request": "launch", "mode": "debug", "program": "${workspaceRoot}/src", "args": ["config", "export"]}, {"name": "Export Image", "type": "go", "request": "launch", "mode": "debug", "program": "${workspaceRoot}/src", "args": ["config", "export", "image"]}, {"name": "Migrate config", "type": "go", "request": "launch", "mode": "debug", "program": "${workspaceRoot}/src", "args": ["config", "migrate"]}, {"name": "Migrate glyphs", "type": "go", "request": "launch", "mode": "debug", "program": "${workspaceRoot}/src", "args": ["config", "migrate", "glyphs"]}, {"name": "Get value", "type": "go", "request": "launch", "mode": "debug", "program": "${workspaceRoot}/src", "args": ["get", "accent"]}, {"name": "Toggle segment", "type": "go", "request": "launch", "mode": "debug", "program": "${workspaceRoot}/src", "args": ["toggle", "git"]}, {"name": "Notice", "type": "go", "request": "launch", "mode": "debug", "program": "${workspaceRoot}/src", "args": ["notice"]}, {"name": "Upgrade", "type": "go", "request": "launch", "mode": "debug", "program": "${workspaceRoot}/src", "args": ["upgrade"]}, {"name": "Font install", "type": "go", "request": "launch", "mode": "debug", "program": "${workspaceRoot}/src", "args": ["font", "install", "AnonymousPro"]}, {"name": "Auth YTMDA", "type": "go", "request": "launch", "mode": "debug", "program": "${workspaceRoot}/src", "args": ["auth", "ytmda"]}, {"type": "node", "request": "launch", "name": "Theme export", "cwd": "${workspaceFolder}/website", "program": "${workspaceRoot}/website/export_themes.js", "console": "integratedTerminal"}, {"type": "node", "request": "launch", "name": "<PERSON><PERSON>", "cwd": "${workspaceFolder}/scripts/bluesky", "program": "${workspaceRoot}/scripts/bluesky/main.cjs", "console": "integratedTerminal", "envFile": "${workspaceFolder}/scripts/bluesky/.env"}, {"name": "Docs API", "type": "node", "request": "attach", "port": 9229, "preLaunchTask": "func: host start", "cwd": "${workspaceFolder}/website", "envFile": "${workspaceFolder}/website/.env"}, {"name": "Cache clear", "type": "go", "request": "launch", "mode": "debug", "program": "${workspaceRoot}/src", "args": ["cache", "clear"]}]}