<!-- markdownlint-disable -->
<p align="center">
  <img
    width="400"
    src="https://raw.githubusercontent.com/jandedobbeleer/oh-my-posh/main/website/static/img/logo.png"
    alt="Oh My Posh logo – Prompt theme engine for any shell"
  />
</p>
<!-- markdownlint-enable -->

![MIT license badge](https://img.shields.io/github/license/JanDeDobbeleer/oh-my-posh.svg)

![Build Status badge](https://img.shields.io/github/actions/workflow/status/jandedobbeleer/oh-my-posh/release.yml?branch=main)

[![Release version number badge][release-badge]][release]

[![Documentation link badge ohmyposh.dev][docs-badge]][docs]

![Number of GitHub Downloads badge](https://img.shields.io/github/downloads/jandedobbeleer/oh-my-posh/total?color=pink&label=GitHub%20Downloads)

<!-- markdownlint-disable -->
<div align="center" markdown="1">
   <sup>Special thanks to:</sup>
   <br>
   <br>
   <a href="https://www.warp.dev/oh-my-posh">
      <img alt="Warp sponsorship" width="400" src="https://github.com/user-attachments/assets/c21102f7-bab9-4344-a731-0cf6b341cab2">
   </a>

### [Warp, the intelligent terminal for developers](https://www.warp.dev/oh-my-posh)
[Available for MacOS, Linux, & Windows](https://www.warp.dev/oh-my-posh)<br>

</div>
<hr>
<!-- markdownlint-enable -->

This repo was made with love using GitKraken.

[![GitKraken shield][kraken]][kraken-ref]
<!-- markdownlint-disable first-header-h1 -->

## Join the community

![Mastodon badge](https://img.shields.io/mastodon/follow/110275292073181892?domain=https%3A%2F%2Fhachyderm.io&label=Mastodon&style=social)

![Discord badge](https://img.shields.io/discord/1023597603331526656)

What started as the offspring of [oh-my-posh2](https://github.com/JanDeDobbeleer/oh-my-posh2) for PowerShell
resulted in a cross platform, highly customizable and extensible prompt theme engine. After 4 years of working
on oh-my-posh, a modern and more efficient tool was needed to suit my personal needs.

## :heart: Support :heart:

[![Swag][swag-badge]][swag] - Show your love with a t-shirt!

[![GitHub][github-badge]][github-sponsors] - One time support, or a recurring donation?

[![Ko-Fi][kofi-badge]][kofi] - No coffee, no code.

<!-- markdownlint-disable -->
<a href="https://polar.sh/oh-my-posh">
  <picture>
    <source media="(prefers-color-scheme: dark)" srcset="https://polar.sh/embed/tiers.svg?org=oh-my-posh&darkmode">
    <img alt="Subscription Tiers on Polar" src="https://polar.sh/embed/tiers.svg?org=oh-my-posh">
  </picture>
</a>
<!-- markdownlint-enable -->

## Features

* Shell and platform agnostic
* Easily configurable
* The **most** configurable prompt utility
* Fast
* Secondary prompt
* Right prompt
* Transient prompt

## Documentation

[![Documentation][docs-badge]][docs]

## Reviews

* [Repo review](https://repo-reviews.github.io//reviews/2023-06-21_TameWizard_JanDeDobbeleer_oh-my-posh) by [TameWizard](https://github.com/TameWizard)

## Thanks

* [Chris Benti](https://github.com/chrisbenti/PS-Config) providing the first influence to start oh-my-posh
* [Keith Dahlby](https://github.com/dahlbyk/posh-git) for creating posh-git and making life more enjoyable
* [Robby Russell](https://github.com/ohmyzsh/ohmyzsh) for creating oh-my-zsh, without him this would probably not be here
* [Janne Mareike Koschinski](https://github.com/justjanne) for providing information on how to get certain information
using Go (and the amazing [README](https://github.com/justjanne/powerline-go))
* [Starship](https://github.com/starship/starship/blob/master/src/init/mod.rs) for doing great things

[kraken]: https://img.shields.io/badge/GitKraken-Legendary%20Git%20Tools-teal?style=plastic&logo=gitkraken
[kraken-ref]: https://www.gitkraken.com/invite/nQmDPR9D
[swag-badge]: https://img.shields.io/badge/Swag-Get%20some!-blue
[swag]: https://swag.ohmyposh.dev
[github-badge]: https://img.shields.io/badge/-Sponsor-fafbfc?logo=GitHub%20Sponsors
[github-sponsors]: https://github.com/sponsors/JanDeDobbeleer
[kofi-badge]: https://img.shields.io/badge/Ko--fi-Buy%20me%20a%20coffee!-%2346b798.svg
[kofi]: https://ko-fi.com/jandedobbeleer
[docs-badge]: https://img.shields.io/badge/Docs-ohmyposh.dev-blue
[docs]: https://ohmyposh.dev
[release-badge]: https://img.shields.io/github/v/release/jandedobbeleer/oh-my-posh?label=Release
[release]: https://github.com/JanDeDobbeleer/oh-my-posh/releases/latest
