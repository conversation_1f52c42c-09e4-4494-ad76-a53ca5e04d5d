<#
.SYNOPSIS
    Checks if a specified directory exists in the system or user PATH environment variable.

.DESCRIPTION
    This script checks whether a specified installation directory is already present
    in either the user or system PATH environment variables. It handles environment
    variable expansion and path normalization to ensure accurate comparison.
    
    The script returns:
    - Exit code 0 if the directory is found in PATH environment variable
    - Exit code 1 if the directory is not found in PATH environment variable

.PARAMETER InstallDir
    The installation directory path to check for in the PATH environment variable.

.EXAMPLE
    .\CheckPath.ps1 -InstallDir "C:\Program Files\Oh My Posh\"
    
    Checks if "C:\Program Files\Oh My Posh\" is in the PATH environment variable.

.EXAMPLE
    .\CheckPath.ps1 -InstallDir "%ProgramFiles%\Oh My Posh\"
    
    Checks if the expanded path of "%ProgramFiles%\Oh My Posh\" is in the PATH environment variable.

.NOTES
    File Name      : CheckPath.ps1
    Author         : <PERSON> (for <PERSON>)
    Prerequisite   : PowerShell 5.1 or later
    Copyright      : (c) 2025 Tony Walsh
#>

param(
    [string]$InstallDir
)

# Get the current PATH environment variable
$userPath = [Environment]::GetEnvironmentVariable("Path", "User")
$systemPath = [Environment]::GetEnvironmentVariable("Path", "Machine")
$fullPath = "$userPath;$systemPath"

# Split the PATH environment variable into individual entries
$pathEntries = $fullPath -split ";"

# Check if any entry, after expanding environment variables, matches the install directory
foreach ($entry in $pathEntries) {
    if ($entry) {
        # Expand any environment variables
        $expandedEntry = [Environment]::ExpandEnvironmentVariables($entry)
        
        # Normalize paths (remove trailing slashes, standardize separators)
        $normalizedEntry = $expandedEntry.TrimEnd('\').Replace('/', '\')
        $normalizedInstallDir = $InstallDir.TrimEnd('\').Replace('/', '\')
        
        # Case-insensitive comparison
        if ($normalizedEntry -eq $normalizedInstallDir) {
            # Path exists, exit with success (0)
            Write-Host "Path exists"
            exit 0
        }
    }
}

# Path doesn't exist, exit with failure (1)
Write-Host "Path does not exist"
exit 1
