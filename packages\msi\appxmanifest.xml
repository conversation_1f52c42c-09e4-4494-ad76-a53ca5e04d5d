﻿<?xml version="1.0" encoding="utf-8"?>
<Package
      xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10"
      xmlns:uap="http://schemas.microsoft.com/appx/manifest/uap/windows10"
      xmlns:rescap="http://schemas.microsoft.com/appx/manifest/foundation/windows10/restrictedcapabilities"
      xmlns:uap3="http://schemas.microsoft.com/appx/manifest/uap/windows10/3"
      xmlns:uap5="http://schemas.microsoft.com/appx/manifest/uap/windows10/5">

  <Identity
    Name="ohmyposh.cli"
    Version=""
    Publisher="CN=<PERSON>, O=<PERSON>, L=Diest, C=BE"
    ProcessorArchitecture="" />

  <Properties>
    <DisplayName>Oh My Posh</DisplayName>
    <PublisherDisplayName><PERSON></PublisherDisplayName>
    <Description>A prompt theme engine for any shell.</Description>
    <Logo>icons\icon.png</Logo>
  </Properties>

  <Resources>
    <Resource Language="en-us" />
  </Resources>

  <Dependencies>
    <TargetDeviceFamily Name="Windows.Desktop" MinVersion="10.0.14316.0" MaxVersionTested="10.0.15063.0" />
  </Dependencies>

  <Capabilities>
    <rescap:Capability Name="runFullTrust" />
  </Capabilities>

  <Applications>
    <Application Id="OhMyPosh" Executable="oh-my-posh.exe" EntryPoint="Windows.FullTrustApplication">
      <uap3:VisualElements
        DisplayName="Oh My Posh"
        Description="Oh My Posh CLI"
        Square150x150Logo="icons\icon.png"
        Square44x44Logo="icons\44.png"
        BackgroundColor="#373e5f"
        AppListEntry="default"
        VisualGroup="Oh My Posh">
      </uap3:VisualElements>

      <Extensions>
        <uap5:Extension Category="windows.appExecutionAlias">
          <uap5:AppExecutionAlias>
            <uap5:ExecutionAlias Alias="oh-my-posh.exe" />
          </uap5:AppExecutionAlias>
        </uap5:Extension>
      </Extensions>
    </Application>
  </Applications>
</Package>
