<Wix xmlns="http://wixtoolset.org/schemas/v4/wxs">
	<Package Language="1033" Manufacturer="<PERSON>" Name="Oh My Posh" UpgradeCode="b2a6bcaa-bda3-4c7f-8dec-d8665d7a9b69" Version="$(env.VERSION)" Scope="perUserOrMachine">
		<SummaryInformation Description="https://ohmyposh.dev" />
		<Icon Id="icon.ico" SourceFile="icon.ico" />
		<Property Id="ARPPRODUCTICON" Value="icon.ico" />
		<Property Id="INSTALLER" Value="manual" />
		<Property Id="PATH_EXISTS" Value="0" />
		<MediaTemplate EmbedCab="yes" />
		<StandardDirectory Id="ProgramFilesFolder">
			<Directory Id="ParentInstallDir" Name="oh-my-posh">
				<Directory Id="INSTALLDIR" Name="bin" />
				<Directory Id="THEMESDIR" Name="themes" />
			</Directory>
		</StandardDirectory>
		<Feature Id="Installation" AllowAbsent="no" Description="https://ohmyposh.dev" Title="Oh My Posh">
			<ComponentGroupRef Id="Files" />
			<ComponentGroupRef Id="EnvironmentVariables" />
		</Feature>
		<InstallExecuteSequence>
			<RemoveExistingProducts After="InstallValidate" />
		</InstallExecuteSequence>
		<CustomAction Id="CheckPath" 
				  Script="powershell"
				  Execute="immediate" 
				  Return="check">
			<![CDATA[
			$installDir = "[INSTALLDIR]"
			$normalizedInstallDir = $installDir.TrimEnd('\').Replace('/', '\')
			$userPath = [Environment]::GetEnvironmentVariable("Path", "User")
			$systemPath = [Environment]::GetEnvironmentVariable("Path", "Machine")
			$fullPath = "$userPath;$systemPath"
			$pathEntries = $fullPath -split ";"
			foreach ($entry in $pathEntries) {
				if ($entry) {
					$expandedEntry = [Environment]::ExpandEnvironmentVariables($entry)
					$normalizedEntry = $expandedEntry.TrimEnd('\').Replace('/', '\')
					if ($normalizedEntry -eq $normalizedInstallDir) {
						exit 0
					}
				}
			}
			exit 1
			]]>
		</CustomAction>
		<InstallUISequence>
			<Custom Action="CheckPath" After="CostFinalize">NOT Installed</Custom>
		</InstallUISequence>
	</Package>
	<Fragment>
		<ComponentGroup Id="Files">
			<Component Id="Executable" Directory="INSTALLDIR">
				<File Source="dist/oh-my-posh.exe" />
			</Component>
			<Files Directory="THEMESDIR" Include="../../themes/*.omp.*" />
		</ComponentGroup>
	</Fragment>
	<Fragment>
		<ComponentGroup Id="EnvironmentVariables">
			<Component Id="SystemEnvironmentVariables" Condition="ALLUSERS=1 AND PATH_EXISTS=0" Directory="INSTALLDIR" Guid="ee903196-9032-4592-b461-25c96823bed7">
				<Environment Id="SystemPath" Action="set" Name="Path" Part="first" Permanent="no" System="yes" Value="[INSTALLDIR]" />
				<Environment Id="SystemThemesDir" Action="set" Name="POSH_THEMES_PATH" Part="all" Permanent="no" System="yes" Value="[THEMESDIR]" />
				<Environment Id="SystemInstaller" Action="set" Name="POSH_INSTALLER" Part="all" Permanent="no" System="yes" Value="[INSTALLER]" />
			</Component>
			<Component Id="UserEnvironmentVariables" Condition="NOT ALLUSERS AND PATH_EXISTS=0" Directory="INSTALLDIR" Guid="212dc904-b134-45b6-a647-a05ce417684a">
				<Environment Id="UserPath" Action="set" Name="Path" Part="first" Permanent="no" System="no" Value="[INSTALLDIR]" />
				<Environment Id="UserThemesDir" Action="set" Name="POSH_THEMES_PATH" Part="all" Permanent="no" System="no" Value="[THEMESDIR]" />
				<Environment Id="UserInstaller" Action="set" Name="POSH_INSTALLER" Part="all" Permanent="no" System="no" Value="[INSTALLER]" />
			</Component>
		</ComponentGroup>
	</Fragment>
</Wix>

