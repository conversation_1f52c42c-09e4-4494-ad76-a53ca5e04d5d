version: "2"
run:
  allow-parallel-runners: true
linters:
  default: none
  enable:
    - bodyclose
    - copyloopvar
    - dupl
    - errcheck
    - exhaustive
    - goconst
    - gocritic
    - gocyclo
    - goprintffuncname
    - govet
    - ineffassign
    - lll
    - misspell
    - nakedret
    - noctx
    - nolintlint
    - revive
    - rowserrcheck
    - staticcheck
    - unconvert
    - unparam
    - unused
    - whitespace
  settings:
    gocritic:
      enabled-tags:
        - diagnostic
        - opinionated
        - performance
        - style
      disabled-tags:
        - experimental
    lll:
      line-length: 180
  exclusions:
    generated: lax
    presets:
      - comments
      - common-false-positives
      - legacy
      - std-error-handling
    paths:
      - third_party$
      - builtin$
      - examples$
formatters:
  enable:
    - gofmt
    - goimports
  exclusions:
    generated: lax
    paths:
      - third_party$
      - builtin$
      - examples$
