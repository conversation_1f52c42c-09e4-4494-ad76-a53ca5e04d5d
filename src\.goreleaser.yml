# Make sure to check the documentation at https://goreleaser.com
# yaml-language-server: $schema=https://goreleaser.com/static/schema.json
version: 2
before:
  hooks:
    - go mod tidy
    - go install github.com/tc-hib/go-winres@latest
    - go-winres make --product-version=git-tag --file-version=git-tag --arch="amd64,386,arm64"
builds:
  -
    binary: "posh-{{ .Os }}-{{ .Arch }}"
    no_unique_dist_dir: true
    flags:
      - -a
    ldflags:
      - -s -w
      - -X github.com/jandedobbeleer/oh-my-posh/src/build.Version={{ .Version }}
      - -X github.com/jandedobbeleer/oh-my-posh/src/build.Date={{ .Date }}
      - -extldflags "-static"
    tags:
      - netgo
      - osusergo
      - static_build
      - timetzdata
    env:
      - CGO_ENABLED=0
    goos:
      - linux
      - windows
      - darwin
      - freebsd
    goarch:
      - amd64
      - arm64
      - arm
      - "386"
    ignore:
      - goos: darwin
        goarch: "386"
      - goos: darwin
        goarch: arm
      - goos: windows
        goarch: arm
    hooks:
      post:
        - pwsh -c "if ('{{ .Path }}'.EndsWith('.exe')) { & '{{ .Env.SIGNTOOL }}' sign /v /debug /fd SHA256 /tr 'http://timestamp.acs.microsoft.com' /td SHA256 /dlib '{{ .Env.SIGNTOOLDLIB }}' /dmdf './metadata.json' '{{ .Path }}' }"
archives:
  - id: oh-my-posh
    format: binary
    name_template: "posh-{{ .Os }}-{{ .Arch }}"
checksum:
  name_template: 'checksums.txt'
signs:
  - cmd: pwsh
    args:
      - "-c"
      - "& '{{ .Env.OPENSSL }}' pkeyutl -sign -inkey '{{ .Env.SHA_SIGNING_KEY_LOCATION }}' -out '${artifact}.sig' -rawin -in '${artifact}'"
    artifacts: checksum
changelog:
  disable: true
