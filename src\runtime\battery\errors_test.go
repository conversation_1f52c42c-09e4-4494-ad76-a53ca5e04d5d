// battery
// Copyright (C) 2016-2017 <PERSON><PERSON> '<PERSON><PERSON>' W<PERSON>
//
// Permission is hereby granted, free of charge, to any person obtaining
// a copy of this software and associated documentation files (the "Software"),
// to deal in the Software without restriction, including without limitation
// the rights to use, copy, modify, merge, publish, distribute, sublicense,
// and/or sell copies of the Software, and to permit persons to whom the
// Software is furnished to do so, subject to the following conditions:
//
// The above copyright notice and this permission notice shall be included
// in all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
// EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
// OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
// IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,
// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
// TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE
// OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

package battery

import (
	"errors"
	"testing"
)

func TestErrors(t *testing.T) {
	cases := []struct {
		str string
		in  Errors
	}{
		{"", Errors{nil}},
		{"", Errors{errors.New("")}},
		{"t1", Errors{errors.New("t1")}},
		{"t2, t3", Errors{errors.New("t2"), errors.New("t3")}},
		{"t4, t5", Errors{errors.New("t4"), errors.New("t5")}},
	}

	for i, c := range cases {
		str := c.in.Error()

		if str != c.str {
			t.Errorf("%d: %v != %v", i, str, c.str)
		}
	}
}
