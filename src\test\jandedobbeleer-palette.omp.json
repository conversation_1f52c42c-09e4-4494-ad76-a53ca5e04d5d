{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "segments": [{"background": "p:session", "foreground": "p:white", "leading_diamond": "", "style": "diamond", "template": " {{ .<PERSON>r<PERSON><PERSON> }} ", "trailing_diamond": "", "type": "session"}, {"background": "p:path", "foreground": "p:white", "powerline_symbol": "", "properties": {"folder_separator_icon": "  ", "home_icon": "~", "style": "folder"}, "style": "powerline", "template": "   {{ .Path }} ", "type": "path"}, {"background": "p:git", "background_templates": ["{{ if or (.Working.Changed) (.Staging.Changed) }}p:git-modified{{ end }}", "{{ if and (gt .Ahead 0) (gt .Behind 0) }}p:git-diverged{{ end }}", "{{ if gt .Ahead 0 }}p:git-ahead{{ end }}", "{{ if gt .Behind 0 }}p:git-behind{{ end }}"], "foreground": "p:git-foreground", "leading_diamond": "", "powerline_symbol": "", "properties": {"fetch_stash_count": true, "fetch_status": true, "fetch_upstream_icon": true}, "style": "powerline", "template": " {{ .UpstreamIcon }}{{ .HEAD }}{{if .BranchStatus }} {{ .BranchStatus }}{{ end }}{{ if .Working.Changed }}  {{ .Working.String }}{{ end }}{{ if and (.Working.Changed) (.Staging.Changed) }} |{{ end }}{{ if .Staging.Changed }}  {{ .Staging.String }}{{ end }}{{ if gt .StashCount 0 }}  {{ .StashCount }}{{ end }} ", "trailing_diamond": "", "type": "git"}, {"background": "p:node", "foreground": "p:white", "powerline_symbol": "", "properties": {"fetch_version": true}, "style": "powerline", "template": "  {{ if .PackageManagerIcon }}{{ .PackageManagerIcon }} {{ end }}{{ .Full }} ", "type": "node"}, {"background": "p:go", "foreground": "p:black", "powerline_symbol": "", "properties": {"fetch_version": true}, "style": "powerline", "template": "  {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }} ", "type": "go"}, {"background": "p:julia", "foreground": "p:black", "powerline_symbol": "", "properties": {"fetch_version": true}, "style": "powerline", "template": "  {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }} ", "type": "julia"}, {"background": "p:python", "foreground": "p:black", "powerline_symbol": "", "properties": {"display_mode": "files", "fetch_virtual_env": false}, "style": "powerline", "template": "  {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }} ", "type": "python"}, {"background": "p:ruby", "foreground": "p:white", "powerline_symbol": "", "properties": {"display_mode": "files", "fetch_version": true}, "style": "powerline", "template": "  {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }} ", "type": "ruby"}, {"background": "p:a<PERSON><PERSON><PERSON>", "foreground": "p:white", "powerline_symbol": "", "properties": {"display_mode": "files", "fetch_version": false}, "style": "powerline", "template": " {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }} ", "type": "azfunc"}, {"background_templates": ["{{if contains \"default\" .Profile}}p:aws-default{{end}}", "{{if contains \"jan\" .Profile}}p:aws-jan{{end}}"], "foreground": "p:white", "powerline_symbol": "", "properties": {"display_default": false}, "style": "powerline", "template": "  {{ .Profile }}{{ if .Region }}@{{ .Region }}{{ end }} ", "type": "aws"}, {"background": "p:root", "foreground": "p:black", "powerline_symbol": "", "style": "powerline", "template": "  ", "type": "root"}, {"background": "p:executiontime", "foreground": "p:white", "properties": {"always_enabled": true}, "style": "plain", "template": "<transparent></> {{ .FormattedMs }}⠀", "type": "executiontime"}, {"background": "p:exit", "background_templates": ["{{ if gt .Code 0 }}p:exit-red{{ end }}"], "foreground": "p:white", "properties": {"always_enabled": true}, "style": "diamond", "template": "<parentBackground></>  ", "trailing_diamond": "", "type": "status"}], "type": "prompt"}, {"segments": [{"background": "p:shell", "foreground": "p:white", "style": "plain", "template": "<#0077c2,transparent></>  {{ .Name }} <transparent,#0077c2></>", "type": "shell"}, {"background": "p:ytm", "foreground": "p:black", "invert_powerline": true, "powerline_symbol": "", "properties": {"paused_icon": " ", "playing_icon": " "}, "style": "powerline", "template": "  {{ .Icon }}{{ if ne .Status \"stopped\" }}{{ .Artist }} - {{ .Track }}{{ end }} ", "type": "ytm"}, {"background": "p:battery", "background_templates": ["{{if eq \"Charging\" .State.String}}p:battery-charging{{end}}", "{{if eq \"Discharging\" .State.String}}p:battery-discharging{{end}}", "{{if eq \"Full\" .State.String}}p:battery-charged{{end}}"], "foreground": "p:white", "invert_powerline": true, "powerline_symbol": "", "properties": {"charged_icon": " ", "charging_icon": " ", "discharging_icon": " "}, "style": "powerline", "template": " {{ if not .Error }}{{ .Icon }}{{ .Percentage }}{{ end }}{{ .Error }} ", "type": "battery"}, {"background": "p:time", "foreground": "p:black", "invert_powerline": true, "leading_diamond": "", "style": "diamond", "template": " {{ .CurrentDate | date .Format }} ", "trailing_diamond": "", "type": "time"}], "type": "rprompt"}], "console_title_template": "{{ .Shell }} in {{ .Folder }}", "final_space": true, "palette": {"aws-default": "#FFA400", "aws-jan": "#F1184C", "azfunc": "#FEAC19", "battery": "#F36943", "battery-charged": "#4CAF50", "battery-charging": "#40C4FF", "battery-discharging": "#FF5722", "black": "#111111", "executiontime": "#83769C", "exit": "#00897B", "exit-red": "#E91E63", "git": "#FFFB38", "git-ahead": "#B388FF", "git-behind": "#B388FF", "git-diverged": "#FF4500", "git-foreground": "#193549", "git-modified": "#FF9248", "go": "#8ED1F7", "julia": "#4063D8", "node": "#6CA35E", "path": "#FF479C", "python": "#FFDE57", "root": "#FFFF66", "ruby": "#AE1401", "session": "#C386F1", "shell": "#0077C2", "time": "#2E9599", "white": "#FFFFFF", "ytm": "#1BD760"}, "version": 3}