<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\UmbracoCms.7.15.7\build\UmbracoCms.props" Condition="Exists('..\packages\UmbracoCms.7.15.7\build\UmbracoCms.props')" />
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" />
  <Import Project="..\packages\uSync.3.2.2.740\build\uSync.props" Condition="Exists('..\packages\uSync.3.2.2.740\build\uSync.props')" />
  <Import Project="..\packages\Microsoft.Net.Compilers.1.0.0\build\Microsoft.Net.Compilers.props" Condition="Exists('..\packages\Microsoft.Net.Compilers.1.0.0\build\Microsoft.Net.Compilers.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{EA5AED40-16B4-41F6-98BB-C4EE8F6B1893}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>MyOtherProject.Site</RootNamespace>
    <AssemblyName>MyOtherProject.Site</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <IISExpressSSLPort>44333</IISExpressSSLPort>
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <Use64BitIISExpress />
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>12.0</OldToolsVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AngularGoogleMaps, Version=2.0.1.35, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\AngularGoogleMaps.2.0.1\lib\net45\AngularGoogleMaps.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Archetype, Version=1.13.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Archetype.Binaries.1.13.1\lib\net40\Archetype.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="AutoMapper, Version=3.3.1.0, Culture=neutral, PublicKeyToken=be96cd2c38ef1005, processorArchitecture=MSIL">
      <HintPath>..\packages\AutoMapper.3.3.1\lib\net40\AutoMapper.dll</HintPath>
    </Reference>
    <Reference Include="AutoMapper.Net4, Version=3.3.1.0, Culture=neutral, PublicKeyToken=be96cd2c38ef1005, processorArchitecture=MSIL">
      <HintPath>..\packages\AutoMapper.3.3.1\lib\net40\AutoMapper.Net4.dll</HintPath>
    </Reference>
    <Reference Include="businesslogic, Version=1.0.7706.20575, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\UmbracoCms.Core.7.15.7\lib\net452\businesslogic.dll</HintPath>
    </Reference>
    <Reference Include="ClientDependency.Core, Version=1.9.9.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\ClientDependency.1.9.9\lib\net45\ClientDependency.Core.dll</HintPath>
    </Reference>
    <Reference Include="ClientDependency.Core.Mvc, Version=1.9.3.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\ClientDependency-Mvc5.1.9.3\lib\net45\ClientDependency.Core.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="cms, Version=1.0.7706.20575, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\UmbracoCms.Core.7.15.7\lib\net452\cms.dll</HintPath>
    </Reference>
    <Reference Include="controls, Version=1.0.7706.20576, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\UmbracoCms.Core.7.15.7\lib\net452\controls.dll</HintPath>
    </Reference>
    <Reference Include="CookComputing.XmlRpcV2, Version=*******, Culture=neutral, PublicKeyToken=a7d6e17aa302004d, processorArchitecture=MSIL">
      <HintPath>..\packages\xmlrpcnet.2.5.0\lib\net20\CookComputing.XmlRpcV2.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Diplo.TraceLogViewer, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\DiploTraceLogViewer.2.2.6\lib\net45\Diplo.TraceLogViewer.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Examine, Version=********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Examine.0.1.90\lib\net45\Examine.dll</HintPath>
    </Reference>
    <Reference Include="FoolproofValidation, Version=0.9.5851.39121, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Foolproof.0.9.4518\lib\net45\FoolproofValidation.dll</HintPath>
    </Reference>   
    <Reference Include="HtmlAgilityPack, Version=*******, Culture=neutral, PublicKeyToken=bd319b19eaf3b43a, processorArchitecture=MSIL">
      <HintPath>..\packages\HtmlAgilityPack.1.8.8\lib\Net45\HtmlAgilityPack.dll</HintPath>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib, Version=0.86.0.518, Culture=neutral, PublicKeyToken=1b03e6acf1164f73, processorArchitecture=MSIL">
      <HintPath>..\packages\SharpZipLib.0.86.0\lib\20\ICSharpCode.SharpZipLib.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="ImageProcessor, Version=*********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\ImageProcessor.*********\lib\net452\ImageProcessor.dll</HintPath>
    </Reference>
    <Reference Include="ImageProcessor.Web, Version=**********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\ImageProcessor.Web.**********\lib\net452\ImageProcessor.Web.dll</HintPath>
    </Reference>
    <Reference Include="ImageResizer, Version=*********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\ImageResizer.3.4.3\lib\ImageResizer.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="ImageResizer.Mvc, Version=*********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\ImageResizer.Mvc.3.4.3\lib\ImageResizer.Mvc.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="ImageResizer.Plugins.DiskCache, Version=*********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\ImageResizer.Plugins.DiskCache.3.4.3\lib\ImageResizer.Plugins.DiskCache.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="interfaces, Version=1.0.7706.20568, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\UmbracoCms.Core.7.15.7\lib\net452\interfaces.dll</HintPath>
    </Reference>
    <Reference Include="Jumoo.uSync.BackOffice, Version=3.2.2.740, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\uSync.3.2.2.740\lib\net45\Jumoo.uSync.BackOffice.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Jumoo.uSync.Core, Version=5.5.2.740, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\uSync.Core.5.5.2.740\lib\net45\Jumoo.uSync.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="log4net, Version=2.0.12.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\packages\UmbracoCms.Core.7.15.7\lib\net452\log4net.dll</HintPath>
    </Reference>
    <Reference Include="Log4Net.Async, Version=2.0.4.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Log4Net.Async.2.0.4\lib\net40\Log4Net.Async.dll</HintPath>
    </Reference>
    <Reference Include="Lucene.Net, Version=2.9.4.1, Culture=neutral, PublicKeyToken=85089178b9ac3181, processorArchitecture=MSIL">
      <HintPath>..\packages\Lucene.Net.2.9.4.1\lib\net40\Lucene.Net.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="MailChimp.Net, Version=4.2.1.0, Culture=neutral, PublicKeyToken=37d26d538413c581">
      <HintPath>..\packages\MailChimp.Net.V3.4.2.1\lib\net45\MailChimp.Net.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="MailChimp.Net.V3, Version=1.7.0.0, Culture=neutral, PublicKeyToken=37d26d538413c581, processorArchitecture=MSIL">
      <HintPath>..\packages\MailChimp.Net.V3.1.7.0.0\lib\net45\MailChimp.Net.V3.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="MarkdownSharp, Version=1.14.5.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Markdown.1.14.7\lib\net45\MarkdownSharp.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.Agent.Intercept, Version=2.4.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.Agent.Intercept.2.4.0\lib\net45\Microsoft.AI.Agent.Intercept.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.DependencyCollector, Version=2.4.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.DependencyCollector.2.4.1\lib\net45\Microsoft.AI.DependencyCollector.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.PerfCounterCollector, Version=2.4.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.PerfCounterCollector.2.4.1\lib\net45\Microsoft.AI.PerfCounterCollector.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.ServerTelemetryChannel, Version=2.4.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.2.4.0\lib\net45\Microsoft.AI.ServerTelemetryChannel.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.Web, Version=2.4.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.Web.2.4.1\lib\net45\Microsoft.AI.Web.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.WindowsServer, Version=2.4.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.WindowsServer.2.4.1\lib\net45\Microsoft.AI.WindowsServer.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ApplicationBlocks.Data, Version=1.0.1559.20655, Culture=neutral">
      <HintPath>..\packages\UmbracoCms.Core.7.15.7\lib\net452\Microsoft.ApplicationBlocks.Data.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ApplicationInsights, Version=2.4.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.2.4.0\lib\net45\Microsoft.ApplicationInsights.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ApplicationInsights.Log4NetAppender, Version=2.4.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.Log4NetAppender.2.4.1\lib\net45\Microsoft.ApplicationInsights.Log4NetAppender.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.Core, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Identity.Core.2.2.2\lib\net45\Microsoft.AspNet.Identity.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.Owin, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Identity.Owin.2.2.2\lib\net45\Microsoft.AspNet.Identity.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.SignalR.Core, Version=2.4.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.SignalR.Core.2.4.1\lib\net45\Microsoft.AspNet.SignalR.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.TelemetryCorrelation, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.TelemetryCorrelation.1.0.0\lib\net45\Microsoft.AspNet.TelemetryCorrelation.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CodeAnalysis, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CodeAnalysis.Common.1.0.0\lib\net45\Microsoft.CodeAnalysis.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.CodeAnalysis.CSharp, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CodeAnalysis.CSharp.1.0.0\lib\net45\Microsoft.CodeAnalysis.CSharp.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=2.0.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.IO.RecyclableMemoryStream, Version=1.2.2.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IO.RecyclableMemoryStream.1.2.2\lib\net45\Microsoft.IO.RecyclableMemoryStream.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.4.0.1\lib\net45\Microsoft.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Host.SystemWeb, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Host.SystemWeb.4.0.1\lib\net45\Microsoft.Owin.Host.SystemWeb.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.4.0.1\lib\net45\Microsoft.Owin.Security.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Cookies, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.Cookies.4.0.1\lib\net45\Microsoft.Owin.Security.Cookies.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.OAuth, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.OAuth.4.0.1\lib\net45\Microsoft.Owin.Security.OAuth.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Web.Infrastructure, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Web.Infrastructure.*******\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="MiniProfiler, Version=*******, Culture=neutral, PublicKeyToken=b44f9351044011a3, processorArchitecture=MSIL">
      <HintPath>..\packages\MiniProfiler.2.1.0\lib\net40\MiniProfiler.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="MySql.Data, Version=*******, Culture=neutral, PublicKeyToken=c5687fc88969c44d, processorArchitecture=MSIL">
      <HintPath>..\packages\MySql.Data.6.9.9\lib\net45\MySql.Data.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=********, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.12.0.2\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Ninject, Version=*******, Culture=neutral, PublicKeyToken=c7192dc5380945e7, processorArchitecture=MSIL">
      <HintPath>..\packages\Ninject.*******\lib\net45-full\Ninject.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Ninject.Web.Common, Version=*******, Culture=neutral, PublicKeyToken=c7192dc5380945e7, processorArchitecture=MSIL">
      <HintPath>..\packages\Ninject.Web.Common.*******\lib\net45-full\Ninject.Web.Common.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Ninject.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=c7192dc5380945e7, processorArchitecture=MSIL">
      <HintPath>..\packages\Ninject.MVC3.3.2.1.0\lib\net45-full\Ninject.Web.Mvc.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Nustache.Core, Version=1.16.0.1, Culture=neutral, PublicKeyToken=efd6f3d8f76ecd9f, processorArchitecture=MSIL">
      <HintPath>..\packages\Nustache.1.16.0.1\lib\net20\Nustache.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Nustache.Mvc3, Version=1.13.8.22, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Nustache.Mvc3.1.13.8.22\lib\net40\Nustache.Mvc3.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Our.Umbraco.Ditto, Version=0.10.6091.32633, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Our.Umbraco.Ditto.0.10.0\lib\net45\Our.Umbraco.Ditto.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Our.Umbraco.NestedContent, Version=0.3.6033.18955, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Our.Umbraco.NestedContent.0.3.0\lib\net45\Our.Umbraco.NestedContent.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Owin, Version=*******, Culture=neutral, PublicKeyToken=f0ebd12fd5e55cc5, processorArchitecture=MSIL">
      <HintPath>..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RazorEngine, Version=3.4.1.0, Culture=neutral, PublicKeyToken=9ee697374c7e744a, processorArchitecture=MSIL">
      <HintPath>..\packages\RazorEngine.3.4.1\lib\net45\RazorEngine.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Semver, Version=1.1.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\semver.1.1.2\lib\net451\Semver.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="SQLCE4Umbraco, Version=1.0.7706.20576, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\UmbracoCms.Core.7.15.7\lib\net452\SQLCE4Umbraco.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Immutable, Version=1.1.36.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Collections.Immutable.1.1.36\lib\portable-net45+win8+wp8+wpa81\System.Collections.Immutable.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Data.SqlServerCe, Version=4.0.0.1, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\UmbracoCms.Core.7.15.7\lib\net452\System.Data.SqlServerCe.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.SqlServerCe.Entity, Version=4.0.0.1, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\UmbracoCms.Core.7.15.7\lib\net452\System.Data.SqlServerCe.Entity.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=4.0.2.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.4.4.0\lib\net45\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Net.Http.Formatting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.7\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Metadata, Version=1.0.21.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Reflection.Metadata.1.0.21\lib\portable-net45+win8\System.Reflection.Metadata.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security" />
    <Reference Include="System.Threading.Tasks.Dataflow, Version=4.6.3.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Dataflow.4.9.0\lib\portable-net45+win8+wpa81\System.Threading.Tasks.Dataflow.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\netstandard1.0\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Helpers, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.7\lib\net45\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.WebHost.5.2.7\lib\net45\System.Web.Http.WebHost.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.7\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.7\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="TidyNet, Version=*******, Culture=neutral">
      <HintPath>..\packages\UmbracoCms.Core.7.15.7\lib\net452\TidyNet.dll</HintPath>
    </Reference>
    <Reference Include="umbraco, Version=1.0.7706.20577, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\UmbracoCms.Core.7.15.7\lib\net452\umbraco.dll</HintPath>
    </Reference>
    <Reference Include="Umbraco.Core, Version=1.0.7706.20570, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\UmbracoCms.Core.7.15.7\lib\net452\Umbraco.Core.dll</HintPath>
    </Reference>
    <Reference Include="umbraco.DataLayer, Version=1.0.7706.20574, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\UmbracoCms.Core.7.15.7\lib\net452\umbraco.DataLayer.dll</HintPath>
    </Reference>
    <Reference Include="umbraco.editorControls, Version=1.0.7706.20582, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\UmbracoCms.Core.7.15.7\lib\net452\umbraco.editorControls.dll</HintPath>
    </Reference>
    <Reference Include="umbraco.MacroEngines, Version=1.0.7706.20582, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\UmbracoCms.Core.7.15.7\lib\net452\umbraco.MacroEngines.dll</HintPath>
    </Reference>
    <Reference Include="Umbraco.ModelsBuilder, Version=3.0.10.102, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Umbraco.ModelsBuilder.3.0.10\lib\Umbraco.ModelsBuilder.dll</HintPath>
    </Reference>
    <Reference Include="umbraco.providers, Version=1.0.7706.20577, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\UmbracoCms.Core.7.15.7\lib\net452\umbraco.providers.dll</HintPath>
    </Reference>
    <Reference Include="Umbraco.Web.UI, Version=1.0.7706.20585, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\UmbracoCms.Core.7.15.7\lib\net452\Umbraco.Web.UI.dll</HintPath>
    </Reference>
    <Reference Include="UmbracoExamine, Version=0.7.0.20576, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\UmbracoCms.Core.7.15.7\lib\net452\UmbracoExamine.dll</HintPath>
    </Reference>
    <Reference Include="UrlRewritingNet.UrlRewriter, Version=2.0.7.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\UrlRewritingNet.2.0.7\lib\UrlRewritingNet.UrlRewriter.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="WebActivatorEx, Version=2.0.0.0, Culture=neutral, PublicKeyToken=7b26dc2a43f6a0d4, processorArchitecture=MSIL">
      <HintPath>..\packages\WebActivatorEx.2.0\lib\net40\WebActivatorEx.dll</HintPath>
      <Private>True</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Content Include=".gitignore" />
    <Content Include="500.aspx" />
    <Content Include="App_Plugins\AngularGoogleMaps\2.0.1\agm-mapprovider.js" />
    <Content Include="App_Plugins\AngularGoogleMaps\2.0.1\agm.js" />
    <Content Include="App_Plugins\AngularGoogleMaps\2.0.1\angular-google-maps.2.1.0.js" />
    <Content Include="App_Plugins\AngularGoogleMaps\2.0.1\angular-google-maps.2.1.0.min.js" />
    <Content Include="App_Plugins\AngularGoogleMaps\2.0.1\angular-google-maps.2.3.3.js" />
    <Content Include="App_Plugins\AngularGoogleMaps\2.0.1\angular-google-maps.2.3.3.min.js" />
    <Content Include="App_Plugins\AngularGoogleMaps\2.0.1\angular-simple-logger.min.js" />
    <Content Include="App_Plugins\AngularGoogleMaps\2.0.1\config.coordinatesbehavour.html" />
    <Content Include="App_Plugins\AngularGoogleMaps\2.0.1\config.defaultlocation.html" />
    <Content Include="App_Plugins\AngularGoogleMaps\2.0.1\config.definition.html" />
    <Content Include="App_Plugins\AngularGoogleMaps\2.0.1\config.format.html" />
    <Content Include="App_Plugins\AngularGoogleMaps\2.0.1\config.icon.html" />
    <Content Include="App_Plugins\AngularGoogleMaps\2.0.1\iconanchorpoint.html" />
    <Content Include="App_Plugins\AngularGoogleMaps\2.0.1\iconsize.html" />
    <Content Include="App_Plugins\AngularGoogleMaps\2.0.1\iconurl.html" />
    <Content Include="App_Plugins\AngularGoogleMaps\2.0.1\map-example.png" />
    <Content Include="App_Plugins\AngularGoogleMaps\2.0.1\underscore-min.js" />
    <Content Include="App_Plugins\AngularGoogleMaps\2.0.1\view.css" />
    <Content Include="App_Plugins\AngularGoogleMaps\2.0.1\view.html" />
    <Content Include="App_Plugins\Archetype\assets\logo_50.png" />
    <Content Include="App_Plugins\Archetype\assets\twitter.png" />
    <Content Include="App_Plugins\Archetype\css\archetype.css" />
    <Content Include="App_Plugins\Archetype\js\archetype.js" />
    <Content Include="App_Plugins\Archetype\langs\da-dk.js" />
    <Content Include="App_Plugins\Archetype\langs\da.js" />
    <Content Include="App_Plugins\Archetype\langs\de-de.js" />
    <Content Include="App_Plugins\Archetype\langs\de.js" />
    <Content Include="App_Plugins\Archetype\langs\en-gb.js" />
    <Content Include="App_Plugins\Archetype\langs\en-us.js" />
    <Content Include="App_Plugins\Archetype\langs\en.js" />
    <Content Include="App_Plugins\Archetype\langs\es-es.js" />
    <Content Include="App_Plugins\Archetype\langs\es.js" />
    <Content Include="App_Plugins\Archetype\langs\fr-fr.js" />
    <Content Include="App_Plugins\Archetype\langs\he-il.js" />
    <Content Include="App_Plugins\Archetype\langs\it-it.js" />
    <Content Include="App_Plugins\Archetype\langs\ja-jp.js" />
    <Content Include="App_Plugins\Archetype\langs\ko-kr.js" />
    <Content Include="App_Plugins\Archetype\langs\nb-no.js" />
    <Content Include="App_Plugins\Archetype\langs\nl-nl.js" />
    <Content Include="App_Plugins\Archetype\langs\pl-pl.js" />
    <Content Include="App_Plugins\Archetype\langs\pt-br.js" />
    <Content Include="App_Plugins\Archetype\langs\ru-ru.js" />
    <Content Include="App_Plugins\Archetype\langs\sv-se.js" />
    <Content Include="App_Plugins\Archetype\langs\zh-cn.js" />
    <Content Include="App_Plugins\Archetype\views\archetype.config.developer.dialog.html" />
    <Content Include="App_Plugins\Archetype\views\archetype.config.fieldset.dialog.html" />
    <Content Include="App_Plugins\Archetype\views\archetype.config.html" />
    <Content Include="App_Plugins\Archetype\views\archetype.config.stylescript.dialog.html" />
    <Content Include="App_Plugins\Archetype\views\archetype.default.html" />
    <Content Include="App_Plugins\Archetype\views\archetype.html" />
    <Content Include="App_Plugins\BondOfferDatabaseDownload\lang\en-GB.xml" />
    <Content Include="App_Plugins\DiploTraceLogViewer\backoffice\diplotracelog\detail.html" />
    <Content Include="App_Plugins\DiploTraceLogViewer\backoffice\diplotracelog\edit.html" />
    <Content Include="App_Plugins\DiploTraceLogViewer\backoffice\diplotracelog\EditController.js" />
    <Content Include="App_Plugins\DiploTraceLogViewer\backoffice\diplotracelog\ng-table\ng-table.min.js" />
    <Content Include="App_Plugins\DiploTraceLogViewer\backoffice\diplotracelog\tracelog.css" />
    <Content Include="App_Plugins\DiploTraceLogViewer\readme.txt" />    
    <Content Include="App_Plugins\ModelsBuilder\modelsbuilder.controller.js" />
    <Content Include="App_Plugins\ModelsBuilder\modelsbuilder.htm" />
    <Content Include="App_Plugins\ModelsBuilder\modelsbuilder.resource.js" />
    <Content Include="App_Plugins\NestedContent\Css\nestedcontent.css" />
    <Content Include="App_Plugins\NestedContent\Js\nestedcontent.controllers.js" />
    <Content Include="App_Plugins\NestedContent\Js\nestedcontent.directives.js" />
    <Content Include="App_Plugins\NestedContent\Js\nestedcontent.resources.js" />
    <Content Include="App_Plugins\NestedContent\Views\nestedcontent.doctypepicker.html" />
    <Content Include="App_Plugins\NestedContent\Views\nestedcontent.editor.html" />
    <Content Include="App_Plugins\NestedContent\Views\nestedcontent.html" />
    <Content Include="App_Plugins\TheDashboard\TheDashboard.controller.js" />
    <Content Include="App_Plugins\TheDashboard\TheDashboard.css" />
    <Content Include="App_Plugins\TheDashboard\TheDashboard.html" />
    <Content Include="App_Plugins\TheDashboard\TheDevDashboard.controller.js" />
    <Content Include="App_Plugins\TheDashboard\TheDevDashboard.html" />
    <Content Include="App_Plugins\TheDashboard\umbraco-guy.png" />
    <Content Include="App_Plugins\BondOfferDatabaseDownload\backoffice\bondOfferDatabaseDownloadTree\edit.html" />
    <Content Include="App_Plugins\BondOfferDatabaseDownload\lang\en-US.xml" />
    <Content Include="App_Plugins\uSync\uSyncDashboard.css" />
    <Content Include="App_Plugins\uSync\uSyncDashboard.html" />
    <Content Include="App_Plugins\uSync\uSyncDashboardController.js" />
    <Content Include="App_Plugins\uSync\uSyncDashboardService.js" />
    <Content Include="App_Plugins\uTwit\js\uTwit.js" />
    <Content Include="App_Plugins\uTwit\TwitterOAuth1Callback.aspx" />
    <Content Include="App_Plugins\uTwit\views\uTwit.html" />
    <Content Include="bin\TheDashboard.dll" />
    <Content Include="Client Scripts\mvcfoolproof.unobtrusive.js" />
    <Content Include="Client Scripts\mvcfoolproof.unobtrusive.min.js" />
    <Content Include="Client Scripts\MvcFoolproofJQueryValidation.js" />
    <Content Include="Client Scripts\MvcFoolproofJQueryValidation.min.js" />
    <Content Include="Client Scripts\MvcFoolproofValidation.js" />
    <Content Include="Client Scripts\MvcFoolproofValidation.min.js" />
    <Content Include="Config\grid.editors.config.js" />
    <Content Include="Config\Lang\cs-CZ.user.xml" />
    <Content Include="Config\Lang\da-DK.user.xml" />
    <Content Include="Config\Lang\de-DE.user.xml" />
    <Content Include="Config\Lang\en-GB.user.xml" />
    <Content Include="Config\Lang\en-US.user.xml" />
    <Content Include="Config\Lang\es-ES.user.xml" />
    <Content Include="Config\Lang\fr-FR.user.xml" />
    <Content Include="Config\Lang\he-IL.user.xml" />
    <Content Include="Config\Lang\it-IT.user.xml" />
    <Content Include="Config\Lang\ja-JP.user.xml" />
    <Content Include="Config\Lang\ko-KR.user.xml" />
    <Content Include="Config\Lang\nb-NO.user.xml" />
    <Content Include="Config\Lang\nl-NL.user.xml" />
    <Content Include="Config\Lang\pl-PL.user.xml" />
    <Content Include="Config\Lang\pt-BR.user.xml" />
    <Content Include="Config\Lang\ru-RU.user.xml" />
    <Content Include="Config\Lang\sv-SE.user.xml" />
    <Content Include="Config\Lang\zh-CN.user.xml" />
    <Content Include="Config\UrlRewrites.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Content\Site.css" />
    <Content Include="default.aspx" />
    <Content Include="Global.asax" />
    <Content Include="gulp\scripts\modules\background-image.js" />
    <Content Include="gulp\scripts\modules\cookie-notice.js" />
    <Content Include="gulp\scripts\modules\helpers.js" />
    <Content Include="gulp\scripts\modules\twitter-timeline.js" />
    <Content Include="gulp\scripts\move\lite-yt-embed.js" />
    <Content Include="gulp\scss\move\wysiwyg.css" />
    <Content Include="Emails\_signup.html" />
    <Content Include="favicon.png" />
    <Content Include="gulpfile.js" />
    <Content Include="gulp\scripts\modules\banner.js" />
    <Content Include="gulp\scripts\modules\calc-block.js" />
    <Content Include="gulp\scripts\modules\external.js" />
    <Content Include="gulp\scripts\modules\faqs.js" />
    <Content Include="gulp\scripts\modules\faux-gallery.js" />
    <Content Include="gulp\scripts\modules\forms.js" />
    <Content Include="gulp\scripts\modules\icon-tabs.js" />
    <Content Include="gulp\scripts\modules\map.js" />
    <Content Include="gulp\scripts\modules\our-history.js" />
    <Content Include="gulp\scripts\modules\photo-gallery.js" />
    <Content Include="gulp\scripts\modules\popups.js" />
    <Content Include="gulp\scripts\modules\projects-map.js" />
    <Content Include="gulp\scripts\modules\tabs.js" />
    <Content Include="gulp\scripts\mvcfoolproof.unobtrusive.min.js" />
    <Content Include="gulp\scripts\plugins\cookies.js" />
    <Content Include="gulp\scripts\plugins\hogan.js" />
    <Content Include="gulp\scripts\plugins\jquery.bxslider.js" />
    <Content Include="gulp\scripts\plugins\jquery.timeout.js" />
    <Content Include="gulp\scripts\plugins\jquery.validate.js" />
    <Content Include="gulp\scripts\plugins\jquery.validate.unobtrusive.js" />
    <Content Include="gulp\scripts\plugins\modernizr.js" />
    <Content Include="gulp\scripts\site.js" />
    <Content Include="gulp\scss\modules\_shares-block.scss" />
    <Content Include="gulp\scss\modules\_report-block.scss" />
    <Content Include="gulp\scss\modules\_home-feature-block.scss" />
    <Content Include="gulp\scss\modules\_content-preview.scss" />
    <Content Include="App_Plugins\uTwit\package.manifest" />
    <Content Include="gulp\scss\modules\_calc-block.scss" />
    <Content Include="gulp\scss\modules\_calc-result.scss" />
    <Content Include="gulp\scss\modules\_help.scss" />
    <Content Include="Config\umbracoSettings.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Config\trees.config" />
    <Content Include="Config\ExamineSettings.config" />
    <Content Include="Config\ExamineIndex.config" />
    <Content Include="Config\Dashboard.config" />
    <Content Include="Config\ClientDependency.config" />
    <Content Include="Config\applications.config" />
    <Content Include="gulp\scss\modules\_quick-links.scss" />
    <Content Include="gulp\scss\modules\_help-pin.scss" />
    <Content Include="gulp\scss\modules\_our-history.scss" />
    <Content Include="gulp\scss\modules\_search.scss" />
    <Content Include="Config\uSyncCore.Config" />
    <Content Include="Config\uSyncBackOffice.Config" />
    <Content Include="App_Plugins\uSync\package.manifest" />
    <Content Include="gulp\scss\modules\_contact-block.scss" />
    <Content Include="Emails\SignUp.mustache" />
    <Content Include="Emails\SignUpClientText.mustache" />
    <Content Include="Emails\MasterFooter.mustache" />
    <Content Include="Emails\MasterHeader.mustache" />
    <Content Include="gulp\scss\modules\_bond-offer.scss" />
    <Content Include="Config\imageprocessor\security.config" />
    <Content Include="Config\imageprocessor\processing.config" />
    <Content Include="Config\imageprocessor\cache.config" />
    <Content Include="Config\log4net.config" />
    <Content Include="ApplicationInsights.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Connected Services\Application Insights\ConnectedService.json" />
    <Content Include="gulp\scss\layout\_breadcrumbs.scss" />
    <Content Include="gulp\scss\layout\_awards.scss" />
    <Content Include="App_Plugins\ModelsBuilder\package.manifest" />
    <Content Include="Emails\SharesRegister.mustache" />
    <Content Include="App_Code\ContentHelper.cshtml" />
    <Content Include="gulp\scss\modules\_alert.scss" />
    <Content Include="Config\tinyMceConfig.config" />
    <Content Include="Config\scripting.config" />
    <Content Include="Config\HealthChecks.config" />
    <Content Include="Config\FileSystemProviders.config" />
    <Content Include="Config\feedProxy.config" />
    <Content Include="Config\EmbeddedMedia.config" />
    <Content Include="Config\BaseRestExtensions.config" />
    <Content Include="Config\BackOfficeTours\getting-started.json" />
    <Content Include="Config\404handlers.config" />
    <Content Include="Config\umbracoSettings.UAT.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Config\umbracoSettings.LIVE.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="gulp\scss\modules\_cookie-notice.scss" />
    <None Include="gulp\scss\modules\_tweet-block.scss" />
    <Content Include="images\common\loader.gif" />
    <Content Include="images\common\logo-small.png" />
    <Content Include="images\common\logo.png" />
    <Content Include="images\common\sprite.png" />
    <Content Include="images\email\logo.png" />
    <Content Include="images\email\social-facebook.png" />
    <Content Include="images\email\social-linkedin.png" />
    <Content Include="images\email\social-twitter.png" />
    <Content Include="images\og\logo.png" />
    <Content Include="images\_temp\features-2.png" />
    <Content Include="images\_temp\features-3.png" />
    <Content Include="images\_temp\features-1.png" />
    <Content Include="images\_temp\features-4.png" />
    <Content Include="images\_temp\how-tab.png" />
    <Content Include="packages.config" />
    <Content Include="robots.txt" />
    <Content Include="sitemap.xsl" />
    <Content Include="Views\Shared\Pager.cshtml" />
    <Content Include="Views\Web.config" />
    <Content Include="App_Plugins\NestedContent\package.manifest" />
    <Content Include="gulp\scss\base\_config.scss" />
    <Content Include="gulp\scss\base\_fixes.scss" />
    <Content Include="gulp\scss\base\_forms.scss" />
    <Content Include="gulp\scss\base\_helpers.scss" />
    <Content Include="gulp\scss\base\_print.scss" />
    <Content Include="gulp\scss\base\_reset.scss" />
    <Content Include="gulp\scss\base\_version.scss" />
    <Content Include="gulp\scss\layout\_back-to-top.scss" />
    <Content Include="gulp\scss\layout\_content.scss" />
    <Content Include="gulp\scss\layout\_footer.scss" />
    <Content Include="gulp\scss\layout\_nav.scss" />
    <Content Include="gulp\scss\layout\_sub-nav.scss" />
    <Content Include="gulp\scss\layout\_header.scss" />
    <Content Include="gulp\scss\layout\_newsletter.scss" />
    <Content Include="gulp\scss\layout\_page.scss" />
    <Content Include="gulp\scss\layout\_sitemap.scss" />
    <Content Include="gulp\scss\modules\_grid.scss" />
    <Content Include="gulp\scss\modules\_popups.scss" />
    <Content Include="gulp\scss\modules\_section.scss" />
    <Content Include="gulp\scss\plugins\_bx-slider.scss" />
    <Content Include="gulp\scss\print.scss" />
    <Content Include="gulp\scss\rte.scss" />
    <Content Include="gulp\scss\site-ie.scss" />
    <Content Include="gulp\scss\site.scss" />
    <Content Include="gulp\scss\_cms.scss" />
    <Content Include="gulp\scss\_config.scss" />
    <Content Include="gulp\scss\_forms.scss" />
    <Content Include="gulp\scss\_reset.scss" />
    <Content Include="bower.json" />
    <Content Include="package.json" />
    <Content Include="App_Plugins\TheDashboard\package.manifest" />
    <Content Include="App_Plugins\DiploTraceLogViewer\package.manifest" />
    <Content Include="App_Plugins\DiploTraceLogViewer\backoffice\diplotracelog\ng-table\ng-table.map" />
    <Content Include="Views\Master.cshtml" />
    <Content Include="Views\Page.cshtml" />
    <Content Include="Views\Sitemap.cshtml" />
    <Content Include="Views\Wrapper.cshtml" />
    <Content Include="Views\Homepage.cshtml" />
    <Content Include="Views\Shared\Meta.cshtml" />
    <Content Include="Views\Shared\Footer.cshtml" />
    <Content Include="Views\Shared\Header.cshtml" />
    <Content Include="Views\Shared\Navigation.cshtml" />
    <Content Include="Views\Error404.cshtml" />
    <Content Include="Views\Project.cshtml" />
    <Content Include="Views\Projects.cshtml" />
    <Content Include="Views\Styleguide.cshtml" />
    <Content Include="Views\Shared\Mustache\PopupConfirm.mustache" />
    <Content Include="Views\Shared\Mustache\PopupLaunch.mustache" />
    <Content Include="Views\Shared\Mustache\PopupVideo.mustache" />
    <Content Include="Views\Shared\NewsletterBlock.cshtml" />
    <Content Include="gulp\scss\modules\_social.scss" />
    <Content Include="Views\Shared\Features.cshtml" />
    <Content Include="gulp\scss\modules\_features.scss" />
    <Content Include="gulp\scss\modules\_share.scss" />
    <Content Include="Views\Shared\GridContent.cshtml" />
    <Content Include="gulp\scss\modules\_banner.scss" />
    <Content Include="Views\Shared\Mustache\Banner.mustache" />
    <Content Include="Views\Shared\Banner.cshtml" />
    <Content Include="gulp\scss\modules\_icon-tabs.scss" />
    <Content Include="Views\Shared\Share.cshtml" />
    <Content Include="Views\Shared\SubNavigation.cshtml" />
    <Content Include="gulp\scss\modules\_block.scss" />
    <Content Include="gulp\scss\modules\_promo-block.scss" />
    <Content Include="gulp\scss\modules\_tabs.scss" />
    <Content Include="gulp\scss\modules\_projects-list-item.scss" />
    <Content Include="gulp\scss\modules\_project.scss" />
    <Content Include="gulp\scss\modules\_map.scss" />
    <Content Include="gulp\scss\modules\_faux-gallery.scss" />
    <Content Include="Views\Shared\Mustache\PopupGallery.mustache" />
    <Content Include="Views\Newsletter.cshtml" />
    <Content Include="gulp\scss\modules\_projects-map.scss" />
    <Content Include="Views\Shared\Mustache\PopupProject.mustache" />
    <Content Include="gulp\scss\modules\_project-preview.scss" />
    <Content Include="gulp\scss\modules\_photo-gallery.scss" />
    <Content Include="Views\Blog.cshtml" />
    <Content Include="Views\BlogPost.cshtml" />
    <Content Include="Views\FAQs.cshtml" />
    <Content Include="gulp\scss\modules\_faqs.scss" />
    <Content Include="gulp\scss\modules\_blog.scss" />
    <Content Include="gulp\scss\modules\_pager.scss" />
    <Content Include="gulp\scss\modules\_blog-preview.scss" />
    <Content Include="Views\BlogSection.cshtml" />
    <Content Include="gulp\scss\modules\_blog-post.scss" />
    <Content Include="gulp\scss\modules\_statement.scss" />
    <Content Include="Views\Shared\NewsFeatures.cshtml" />
    <Content Include="Views\Shared\BlogPreview.cshtml" />
    <Content Include="Views\Shared\BlogNavigation.cshtml" />
    <Content Include="Views\Shared\LatestTweets.cshtml" />
    <Content Include="Views\Shared\TweetList.cshtml" />
    <Content Include="Views\Shared\MoreFeatures.cshtml" />
    <Content Include="Views\Shared\Calculator.cshtml" />
    <Content Include="Views\Shared\Mustache\PopupCalcResult.mustache" />
    <Content Include="Media\Web.config" />
    <Content Include="Views\History.cshtml" />
    <Content Include="Views\Shared\Mustache\HistoryTimeline.mustache" />
    <Content Include="Views\Shared\Mustache\PopupSearch.mustache" />
    <Content Include="Views\SearchResults.cshtml" />
    <Content Include="Views\Shared\ContactBlock.cshtml" />
    <Content Include="Views\Shared\SharesBlock.cshtml" />
    <Content Include="Views\offerSignUpFormDigital.cshtml" />
    <Content Include="Views\offerSignUpFormPhysical.cshtml" />
    <Content Include="Views\BondOffer.cshtml" />
    <Content Include="Views\Shared\NewsletterBlockSmall.cshtml" />
    <Content Include="Views\Shared\Breadcrumbs.cshtml" />
    <Content Include="Views\Shared\Awards.cshtml" />
    <Content Include="Views\SharesRegistrationForm.cshtml" />
    <Content Include="Views\Partials\Grid\Editors\Textstring.cshtml" />
    <Content Include="Views\Partials\Grid\Editors\Rte.cshtml" />
    <Content Include="Views\Partials\Grid\Editors\Media.cshtml" />
    <Content Include="Views\Partials\Grid\Editors\Macro.cshtml" />
    <Content Include="Views\Partials\Grid\Editors\Embed.cshtml" />
    <Content Include="Views\Partials\Grid\Editors\Base.cshtml" />
    <Content Include="Views\Partials\Grid\Bootstrap3.cshtml" />
    <Content Include="Views\Partials\Grid\Bootstrap3-Fluid.cshtml" />
    <Content Include="Views\Partials\Grid\Bootstrap2.cshtml" />
    <Content Include="Views\Partials\Grid\Bootstrap2-Fluid.cshtml" />
    <Content Include="gulp\scss\plugins\_lite-yt-embed.scss" />
    <Content Include="Views\_ViewStart.cshtml" />
    <Content Include="Views\Shared\_Layout.cshtml" />
    <Content Include="Views\MasterTextOnly.cshtml" />
    <Content Include="Views\SimplePage.cshtml" />
    <Content Include="Views\WrapperSimple.cshtml" />
    <Content Include="Views\Shared\CookieNotice.cshtml" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <Content Include="Web.UAT.config" />
    <Content Include="Web.LIVE.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="500.aspx.cs">
      <DependentUpon>500.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Analyzers\IgnoreAccentsAnalyzer.cs" />
    <Compile Include="App_Plugins\BondOfferDatabaseDownload\BondOfferDatabaseDownloadController.cs" />
    <Compile Include="App_Plugins\BondOfferDatabaseDownload\BondOfferDatabaseDownloadApplication.cs" />
    <Compile Include="App_Plugins\BondOfferDatabaseDownload\BondOfferDatabaseDownloadTree.cs" />
    <Compile Include="App_Start\NinjectWebCommon.cs" />
    <Compile Include="App_Start\UmbracoStartup.cs" />
    <Compile Include="Controllers\BlogPostController.cs" />
    <Compile Include="App_Start\ViewEngineConfig.cs" />
    <Compile Include="Controllers\BlogSectionController.cs" />
    <Compile Include="Controllers\BondOfferController.cs" />
    <Compile Include="Controllers\OfferSignUpFormDigitalController.cs" />
    <Compile Include="Controllers\Error404Controller.cs" />
    <Compile Include="Controllers\FAQsPageController.cs" />
    <Compile Include="Controllers\HistoryController.cs" />
    <Compile Include="Controllers\HomeController.cs" />
    <Compile Include="Controllers\BlogController.cs" />
    <Compile Include="Controllers\NewsletterController.cs" />
    <Compile Include="Controllers\OfferSignUpFormPhysicalController.cs" />
    <Compile Include="Controllers\ProjectController.cs" />
    <Compile Include="Controllers\ProjectsController.cs" />
    <Compile Include="Controllers\PageController.cs" />
    <Compile Include="Controllers\SearchResultsController.cs" />
    <Compile Include="Controllers\SharesRegisterController.cs" />
    <Compile Include="Controllers\SimplePageController.cs" />
    <Compile Include="Controllers\SitemapController.cs" />
    <Compile Include="Controllers\TwitterController.cs" />
    <Compile Include="ErrorHandler\AiHandleErrorAttribute.cs" />
    <Compile Include="FilterConfig.cs" />
    <Compile Include="Helpers\ImageExtensions.cs" />
    <Compile Include="Helpers\ViewExtensions.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="RouteHandlers\RouteHandler.cs" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Views\Export\" />
    <Folder Include="Views\SharesRegister\" />
    <Folder Include="Views\SimplePage\" />
    <Folder Include="Views\UserDatabaseDownload\" />
  </ItemGroup>
  <ItemGroup>
    <Analyzer Include="..\packages\Microsoft.CodeAnalysis.Analyzers.1.0.0\analyzers\dotnet\cs\Microsoft.CodeAnalysis.Analyzers.dll" />
    <Analyzer Include="..\packages\Microsoft.CodeAnalysis.Analyzers.1.0.0\analyzers\dotnet\cs\Microsoft.CodeAnalysis.CSharp.Analyzers.dll" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>12088</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>https://localhost:44333/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.Net.Compilers.1.0.0\build\Microsoft.Net.Compilers.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Net.Compilers.1.0.0\build\Microsoft.Net.Compilers.props'))" />
    <Error Condition="!Exists('..\packages\uSync.3.2.2.740\build\uSync.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\uSync.3.2.2.740\build\uSync.props'))" />
    <Error Condition="!Exists('..\packages\uSync.3.2.2.740\build\uSync.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\uSync.3.2.2.740\build\uSync.targets'))" />
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props'))" />
    <Error Condition="!Exists('..\packages\UmbracoCms.7.15.7\build\UmbracoCms.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\UmbracoCms.7.15.7\build\UmbracoCms.props'))" />
    <Error Condition="!Exists('..\packages\UmbracoCms.7.15.7\build\UmbracoCms.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\UmbracoCms.7.15.7\build\UmbracoCms.targets'))" />
  </Target>
  <Import Project="..\packages\uSync.3.2.2.740\build\uSync.targets" Condition="Exists('..\packages\uSync.3.2.2.740\build\uSync.targets')" />
  <Import Project="..\packages\AutoMapper.3.3.1\tools\AutoMapper.targets" Condition="Exists('..\packages\AutoMapper.3.3.1\tools\AutoMapper.targets')" />
  <Import Project="..\packages\OctoPack.3.4.2\tools\OctoPack.targets" Condition="Exists('..\packages\OctoPack.3.4.2\tools\OctoPack.targets')" />
  <Target Name="EnsureOctoPackImported" BeforeTargets="BeforeBuild" Condition="'$(OctoPackImported)' == ''">
    <Error Condition="!Exists('..\packages\OctoPack.3.4.2\tools\OctoPack.targets') And ('$(RunOctoPack)' != '' And $(RunOctoPack))" Text="You are trying to build with OctoPack, but the NuGet targets file that OctoPack depends on is not available on this computer. This is probably because the OctoPack package has not been committed to source control, or NuGet Package Restore is not enabled. Please enable NuGet Package Restore to download them. For more information, see http://go.microsoft.com/fwlink/?LinkID=317567." HelpKeyword="BCLBUILD2001" />
    <Error Condition="Exists('..\packages\OctoPack.3.4.2\tools\OctoPack.targets') And ('$(RunOctoPack)' != '' And $(RunOctoPack))" Text="OctoPack cannot be run because NuGet packages were restored prior to the build running, and the targets file was unavailable when the build started. Please build the project again to include these packages in the build. You may also need to make sure that your build server does not delete packages prior to each build. For more information, see http://go.microsoft.com/fwlink/?LinkID=317568." HelpKeyword="BCLBUILD2002" />
  </Target>
  <Import Project="..\packages\UmbracoCms.7.15.7\build\UmbracoCms.targets" Condition="Exists('..\packages\UmbracoCms.7.15.7\build\UmbracoCms.targets')" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>