<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="urlrewritingnet" restartOnExternalChanges="true" requirePermission="false" type="UrlRewritingNet.Configuration.UrlRewriteSection, UrlRewritingNet.UrlRewriter" />
    <section name="microsoft.scripting" type="Microsoft.Scripting.Hosting.Configuration.Section, Microsoft.Scripting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" requirePermission="false" />
    <section name="clientDependency" type="ClientDependency.Core.Config.ClientDependencySection, ClientDependency.Core" requirePermission="false" />
    <section name="Examine" type="Examine.Config.ExamineSettings, Examine" requirePermission="false" />
    <section name="ExamineLuceneIndexSets" type="UmbracoExamine.Config.ExamineLuceneIndexes, UmbracoExamine" requirePermission="false" />
    <section name="FileSystemProviders" type="Umbraco.Core.Configuration.FileSystemProvidersSection, Umbraco.Core" requirePermission="false" />
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net" requirePermission="false" />
    <section name="BaseRestExtensions" type="Umbraco.Web.BaseRest.Configuration.BaseRestSection, umbraco" requirePermission="false" />
    <sectionGroup name="system.web.webPages.razor" type="System.Web.WebPages.Razor.Configuration.RazorWebSectionGroup, System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35">
      <section name="host" type="System.Web.WebPages.Razor.Configuration.HostSection, System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" requirePermission="false" />
      <section name="pages" type="System.Web.WebPages.Razor.Configuration.RazorPagesSection, System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <urlrewritingnet configSource="config\UrlRewriting.config" />
  <microsoft.scripting configSource="config\scripting.config" />
  <clientDependency configSource="config\ClientDependency.config" />
  <Examine configSource="config\ExamineSettings.config" />
  <ExamineLuceneIndexSets configSource="config\ExamineIndex.config" />
  <FileSystemProviders configSource="config\FileSystemProviders.config" />
  <log4net configSource="config\log4net.config" />
  <BaseRestExtensions configSource="config\BaseRestExtensions.config" />
  <appSettings>
    <add key="umbracoDbDSN" value="datalayer=SQLCE4Umbraco.SqlCEHelper,SQLCE4Umbraco;data source=|DataDirectory|\Umbraco.sdf" />
    <add key="umbracoConfigurationStatus" value="4.11.10" />
    <add key="umbracoReservedUrls" value="~/config/splashes/booting.aspx,~/install/default.aspx,~/config/splashes/noNodes.aspx,~/VSEnterpriseHelper.axd" />
    <add key="umbracoReservedPaths" value="~/umbraco,~/install/" />
    <add key="umbracoContentXML" value="~/App_Data/umbraco.config" />
    <add key="umbracoStorageDirectory" value="~/App_Data" />
    <add key="umbracoPath" value="~/umbraco" />
    <add key="umbracoEnableStat" value="false" />
    <add key="umbracoHideTopLevelNodeFromPath" value="true" />
    <add key="umbracoEditXhtmlMode" value="true" />
    <add key="umbracoUseDirectoryUrls" value="false" />
    <add key="umbracoDebugMode" value="false" />
    <add key="umbracoTimeOutInMinutes" value="20" />
    <add key="umbracoVersionCheckPeriod" value="7" />
    <add key="umbracoDisableXsltExtensions" value="true" />
    <add key="umbracoDefaultUILanguage" value="en" />
    <add key="umbracoProfileUrl" value="profiler" />
    <add key="umbracoUseSSL" value="false" />
    <add key="umbracoUseMediumTrust" value="false" />
    <!-- Set this to true to enable storing the xml cache locally to the IIS server even if the app files are stored centrally on a SAN/NAS Alex Norcliffe 2010 02 for 4.1  -->
    <add key="umbracoContentXMLUseLocalTemp" value="false" />
    <add key="webpages:Enabled" value="false" />
    <add key="enableSimpleMembership" value="false" />
    <add key="autoFormsAuthentication" value="false" />
    <add key="log4net.Config" value="config\log4net.config" />
  </appSettings>
  <system.net>
    <mailSettings>
      <smtp>
        <network host="127.0.0.1" userName="username" password="password" />
      </smtp>
    </mailSettings>
  </system.net>
  <connectionStrings>
    <remove name="LocalSqlServer" />
    <!--<add name="LocalSqlServer" connectionString="server=.\sqlexpress;database=aspnetdb;user id=DBUSER;password=DBPASSWORD" providerName="System.Data.SqlClient"/>-->
  </connectionStrings>
  <system.web>
    <customErrors mode="RemoteOnly" />
    <trace enabled="false" requestLimit="10" pageOutput="false" traceMode="SortByTime" localOnly="true" />
    <sessionState mode="InProc" stateConnectionString="tcpip=127.0.0.1:42424" sqlConnectionString="data source=127.0.0.1;Trusted_Connection=yes" cookieless="false" timeout="20" />
    <globalization requestEncoding="UTF-8" responseEncoding="UTF-8" />
    <xhtmlConformance mode="Strict" />
    <httpRuntime requestValidationMode="2.0" enableVersionHeader="false" />
    <pages enableEventValidation="false">
      <!-- ASPNETAJAX -->
      <controls>
        <add tagPrefix="asp" namespace="System.Web.UI" assembly="System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
        <add tagPrefix="umbraco" namespace="umbraco.presentation.templateControls" assembly="umbraco" />
        <add tagPrefix="asp" namespace="System.Web.UI.WebControls" assembly="System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
      </controls>
    </pages>
    <httpModules>
      <!-- URL REWRTIER -->
      <add name="UrlRewriteModule" type="UrlRewritingNet.Web.UrlRewriteModule, UrlRewritingNet.UrlRewriter" />
      <!-- UMBRACO -->
      <add name="UmbracoModule" type="Umbraco.Web.UmbracoModule,umbraco" />
      <!-- ASPNETAJAX -->
      <add name="ScriptModule" type="System.Web.Handlers.ScriptModule, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
      <!-- CLIENT DEPENDENCY -->
      <add name="ClientDependencyModule" type="ClientDependency.Core.Module.ClientDependencyModule, ClientDependency.Core" />
    </httpModules>
    <httpHandlers>
      <remove verb="*" path="*.asmx" />
      <!-- ASPNETAJAX -->
      <add verb="*" path="*.asmx" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" validate="false" />
      <add verb="*" path="*_AppService.axd" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" validate="false" />
      <add verb="GET,HEAD" path="ScriptResource.axd" type="System.Web.Handlers.ScriptResourceHandler, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" validate="false" />
      <!-- UMBRACO CHANNELS -->
      <add verb="*" path="umbraco/channels.aspx" type="umbraco.presentation.channels.api, umbraco" />
      <add verb="*" path="umbraco/channels/word.aspx" type="umbraco.presentation.channels.wordApi, umbraco" />
      <!-- CLIENT DEPENDENCY -->
      <add verb="*" path="DependencyHandler.axd" type="ClientDependency.Core.CompositeFiles.CompositeDependencyHandler, ClientDependency.Core " />
      <!-- SPELL CHECKER -->
      <add verb="GET,HEAD,POST" path="GoogleSpellChecker.ashx" type="umbraco.presentation.umbraco_client.tinymce3.plugins.spellchecker.GoogleSpellChecker,umbraco" />
    </httpHandlers>
    <compilation defaultLanguage="c#" debug="false" batch="false" targetFramework="4.0">
      <assemblies>
        <!-- ASP.NET 4.0 Assemblies -->
        <add assembly="System.Design, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.Core, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
        <add assembly="System.Xml.Linq, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.Data.DataSetExtensions, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.Web.Extensions.Design, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
        <add assembly="System.Web.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
        <add assembly="System.Web.Helpers, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
        <add assembly="System.Web.Routing, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
        <add assembly="System.Web.Mvc, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
        <add assembly="System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
      </assemblies>
      <buildProviders>
        <add extension=".cshtml" type="umbraco.MacroEngines.RazorBuildProvider, umbraco.MacroEngines" />
        <add extension=".vbhtml" type="umbraco.MacroEngines.RazorBuildProvider, umbraco.MacroEngines" />
        <add extension=".razor" type="umbraco.MacroEngines.RazorBuildProvider, umbraco.MacroEngines" />
      </buildProviders>
    </compilation>
    <authentication mode="Forms">
      <forms name="yourAuthCookie" loginUrl="login.aspx" protection="All" path="/" />
    </authentication>
    <authorization>
      <allow users="?" />
    </authorization>
    <!-- Membership Provider -->
    <membership defaultProvider="UmbracoMembershipProvider" userIsOnlineTimeWindow="15">
      <providers>
        <clear />
        <add name="UmbracoMembershipProvider" type="umbraco.providers.members.UmbracoMembershipProvider" enablePasswordRetrieval="false" enablePasswordReset="false" requiresQuestionAndAnswer="false" defaultMemberTypeAlias="Another Type" passwordFormat="Hashed" />
        <add name="UsersMembershipProvider" type="umbraco.providers.UsersMembershipProvider" enablePasswordRetrieval="false" enablePasswordReset="false" requiresQuestionAndAnswer="false" passwordFormat="Hashed" />
      </providers>
    </membership>
    <!-- added by NH to support membership providers in access layer -->
    <roleManager enabled="true" defaultProvider="UmbracoRoleProvider">
      <providers>
        <clear />
        <add name="UmbracoRoleProvider" type="umbraco.providers.members.UmbracoRoleProvider" />
      </providers>
    </roleManager>
    <!-- Sitemap provider-->
    <siteMap defaultProvider="UmbracoSiteMapProvider" enabled="true">
      <providers>
        <clear />
        <add name="UmbracoSiteMapProvider" type="umbraco.presentation.nodeFactory.UmbracoSiteMapProvider" defaultDescriptionAlias="description" securityTrimmingEnabled="true" />
      </providers>
    </siteMap>
  </system.web>
  <!-- ASPNETAJAX -->
  <system.web.extensions>
    <scripting>
      <scriptResourceHandler enableCompression="true" enableCaching="true" />
    </scripting>
  </system.web.extensions>
  <system.webServer>
    <validation validateIntegratedModeConfiguration="false" />
    <modules runAllManagedModulesForAllRequests="true">
      <remove name="UrlRewriteModule" />
      <add name="UrlRewriteModule" type="UrlRewritingNet.Web.UrlRewriteModule, UrlRewritingNet.UrlRewriter" />
      <remove name="UmbracoModule" />
      <add name=" UmbracoModule" type="Umbraco.Web.UmbracoModule,umbraco" />
      <remove name="ScriptModule" />
      <add name="ScriptModule" preCondition="managedHandler" type="System.Web.Handlers.ScriptModule, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
      <remove name="ClientDependencyModule" />
      <add name="ClientDependencyModule" type="ClientDependency.Core.Module.ClientDependencyModule, ClientDependency.Core" />
      <!-- Needed for login/membership to work on homepage (as per http://stackoverflow.com/questions/218057/httpcontext-current-session-is-null-when-routing-requests) -->
      <remove name="FormsAuthentication" />
      <add name="FormsAuthentication" type="System.Web.Security.FormsAuthenticationModule" />
    </modules>
    <handlers accessPolicy="Read, Write, Script, Execute">
      <remove name="WebServiceHandlerFactory-Integrated" />
      <remove name="ScriptHandlerFactory" />
      <remove name="ScriptHandlerFactoryAppServices" />
      <remove name="ScriptResource" />
      <remove name="Channels" />
      <remove name="Channels_Word" />
      <remove name="ClientDependency" />
      <remove name="SpellChecker" />
      <add name="ScriptHandlerFactory" verb="*" path="*.asmx" preCondition="integratedMode" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
      <add name="ScriptHandlerFactoryAppServices" verb="*" path="*_AppService.axd" preCondition="integratedMode" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
      <add name="ScriptResource" verb="GET,HEAD" path="ScriptResource.axd" preCondition="integratedMode" type="System.Web.Handlers.ScriptResourceHandler, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
      <add verb="*" name="Channels" preCondition="integratedMode" path="umbraco/channels.aspx" type="umbraco.presentation.channels.api, umbraco" />
      <add verb="*" name="Channels_Word" preCondition="integratedMode" path="umbraco/channels/word.aspx" type="umbraco.presentation.channels.wordApi, umbraco" />
      <add verb="*" name="ClientDependency" preCondition="integratedMode" path="DependencyHandler.axd" type="ClientDependency.Core.CompositeFiles.CompositeDependencyHandler, ClientDependency.Core " />
      <add verb="GET,HEAD,POST" preCondition="integratedMode" name="SpellChecker" path="GoogleSpellChecker.ashx" type="umbraco.presentation.umbraco_client.tinymce3.plugins.spellchecker.GoogleSpellChecker,umbraco" />
    </handlers>
    <!-- Adobe AIR mime type -->
    <staticContent>
      <remove fileExtension=".air" />
      <mimeMap fileExtension=".air" mimeType="application/vnd.adobe.air-application-installer-package+zip" />
    </staticContent>
    <!-- Ensure the powered by header is not returned -->
    <httpProtocol>
      <customHeaders>
        <remove name="X-Powered-By" />
      </customHeaders>
    </httpProtocol>
  </system.webServer>
  <system.codedom>
    <compilers>
      <compiler language="c#;cs;csharp" extension=".cs" type="Microsoft.CSharp.CSharpCodeProvider,System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" warningLevel="4">
        <providerOption name="CompilerVersion" value="v4.0" />
        <providerOption name="WarnAsError" value="false" />
      </compiler>
    </compilers>
  </system.codedom>
  <runtime>
    <!-- Old asp.net ajax assembly bindings -->
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Extensions" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="*******-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Extensions.Design" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="*******-*******" newVersion="*******" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <system.web.webPages.razor>
    <host factoryType="umbraco.MacroEngines.RazorUmbracoFactory, umbraco.MacroEngines" />
    <pages pageBaseType="umbraco.MacroEngines.DynamicNodeContext">
      <namespaces>
        <add namespace="Microsoft.Web.Helpers" />
        <add namespace="umbraco" />
        <add namespace="Examine" />
      </namespaces>
    </pages>
  </system.web.webPages.razor>
</configuration>