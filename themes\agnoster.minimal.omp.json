{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "segments": [{"foreground": "#ffffff", "style": "plain", "template": "{{ reason .Code }}❌ ", "type": "status"}, {"foreground": "#ff0000", "style": "plain", "template": "# ", "type": "root"}, {"foreground": "#ffffff", "style": "plain", "template": "{{ .UserName }}@{{ .HostName }} ", "type": "session"}, {"background": "#007ACC", "foreground": "#ffffff", "properties": {"folder_icon": "…", "folder_separator_icon": "  ", "style": "agnoster_short", "max_depth": 3}, "style": "plain", "template": "<transparent></> {{ .Path }} ", "type": "path"}, {"background": "#007ACC", "foreground": "#ffffff", "properties": {"cherry_pick_icon": "✓ ", "commit_icon": "▷ ", "fetch_status": true, "merge_icon": "◴ ", "no_commits_icon": "[no commits]", "rebase_icon": "Ɫ ", "tag_icon": "▶ "}, "style": "plain", "template": "{{ .HEAD }}{{ if and (eq .Ahead 0) (eq .Behind 0) }} ≡{{end}}{{ if gt .Ahead 0 }} ↑{{.Ahead}}{{end}}{{ if gt .Behind 0 }} ↓{{.Behind}}{{end}} {{ if .Working.Changed }}+{{ .Working.Added }} ~{{ .Working.Modified }} -{{ .Working.Deleted }} {{ end }}", "type": "git"}, {"foreground": "#007ACC", "style": "plain", "template": " ", "type": "text"}], "type": "prompt"}], "version": 3}