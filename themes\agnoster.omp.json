{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "segments": [{"background": "#ffe9aa", "foreground": "#100e23", "powerline_symbol": "", "style": "powerline", "template": "  ", "type": "root"}, {"background": "#ffffff", "foreground": "#100e23", "powerline_symbol": "", "style": "powerline", "template": " {{ .UserName }}@{{ .HostName }} ", "type": "session"}, {"background": "#91ddff", "foreground": "#100e23", "powerline_symbol": "", "properties": {"folder_icon": "", "folder_separator_icon": "  ", "home_icon": "", "style": "agnoster"}, "style": "powerline", "template": " {{ .Path }} ", "type": "path"}, {"background": "#95ffa4", "foreground": "#193549", "powerline_symbol": "", "style": "powerline", "template": " {{ .HEAD }} ", "type": "git"}, {"background": "#906cff", "foreground": "#100e23", "powerline_symbol": "", "style": "powerline", "template": "  {{ if .Error }}{{ .Error }}{{ else }}{{ if .Venv }}{{ .Venv }} {{ end }}{{ .Full }}{{ end }} ", "type": "python"}, {"background": "#ff8080", "foreground": "#ffffff", "powerline_symbol": "", "style": "powerline", "template": " {{ reason .Code }} ", "type": "status"}], "type": "prompt"}], "final_space": true, "version": 3}