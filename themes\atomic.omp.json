{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "segments": [{"background": "#0077c2", "foreground": "#ffffff", "leading_diamond": "╭─", "style": "diamond", "template": " {{ .Name }} ", "type": "shell"}, {"background": "#ef5350", "foreground": "#FFFB38", "style": "diamond", "template": "<parentBackground></>  ", "type": "root"}, {"background": "#FF9248", "foreground": "#2d3436", "powerline_symbol": "", "properties": {"folder_icon": "  ", "home_icon": "", "style": "folder"}, "style": "powerline", "template": "  {{ .Path }} ", "type": "path"}, {"background": "#FFFB38", "background_templates": ["{{ if or (.Working.Changed) (.Staging.Changed) }}#ffeb95{{ end }}", "{{ if and (gt .Ahead 0) (gt .Behind 0) }}#c5e478{{ end }}", "{{ if gt .Ahead 0 }}#C792EA{{ end }}", "{{ if gt .Behind 0 }}#C792EA{{ end }}"], "foreground": "#011627", "powerline_symbol": "", "properties": {"branch_icon": " ", "fetch_status": true, "fetch_upstream_icon": true}, "style": "powerline", "template": " {{ .UpstreamIcon }}{{ .HEAD }}{{if .BranchStatus }} {{ .BranchStatus }}{{ end }}{{ if .Working.Changed }}  {{ .Working.String }}{{ end }}{{ if and (.Working.Changed) (.Staging.Changed) }} |{{ end }}{{ if .Staging.Changed }}<#ef5350>  {{ .Staging.String }}</>{{ end }} ", "type": "git"}, {"background": "#83769c", "foreground": "#ffffff", "properties": {"style": "roundrock", "threshold": 0}, "style": "diamond", "template": "  {{ .FormattedMs }}⠀", "trailing_diamond": "", "type": "executiontime"}], "type": "prompt"}, {"alignment": "right", "segments": [{"background": "#303030", "foreground": "#3C873A", "leading_diamond": "", "properties": {"fetch_package_manager": true, "npm_icon": " <#cc3a3a></> ", "yarn_icon": " <#348cba></>"}, "style": "diamond", "template": " {{ if .PackageManagerIcon }}{{ .PackageManagerIcon }} {{ end }}{{ .Full }}", "trailing_diamond": " ", "type": "node"}, {"background": "#306998", "foreground": "#FFE873", "leading_diamond": "", "style": "diamond", "template": " {{ if .Error }}{{ .Error }}{{ else }}{{ if .Venv }}{{ .Venv }} {{ end }}{{ .Full }}{{ end }}", "trailing_diamond": " ", "type": "python"}, {"background": "#0e8ac8", "foreground": "#ffffff", "leading_diamond": "", "style": "diamond", "template": " {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }}", "trailing_diamond": " ", "type": "java"}, {"background": "#0e0e0e", "foreground": "#0d6da8", "leading_diamond": "", "style": "diamond", "template": " {{ if .Unsupported }}{{ else }}{{ .Full }}{{ end }}", "trailing_diamond": " ", "type": "dotnet"}, {"background": "#ffffff", "foreground": "#06aad5", "leading_diamond": "", "style": "diamond", "template": " {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }}", "trailing_diamond": " ", "type": "go"}, {"background": "#f3f0ec", "foreground": "#925837", "leading_diamond": "", "style": "diamond", "template": " {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }}", "trailing_diamond": " ", "type": "rust"}, {"background": "#e1e8e9", "foreground": "#055b9c", "leading_diamond": " ", "style": "diamond", "template": " {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }}", "trailing_diamond": " ", "type": "dart"}, {"background": "#ffffff", "foreground": "#ce092f", "leading_diamond": " ", "style": "diamond", "template": " {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }}", "trailing_diamond": " ", "type": "angular"}, {"background": "#ffffff", "foreground": "#de1f84", "leading_diamond": " ", "style": "diamond", "template": "α {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }}", "trailing_diamond": " ", "type": "aurelia"}, {"background": "#1e293b", "foreground": "#ffffff", "leading_diamond": " ", "style": "diamond", "template": "{{ if .Error }}{{ .Error }}{{ else }}Nx {{ .Full }}{{ end }}", "trailing_diamond": " ", "type": "nx"}, {"background": "#945bb3", "foreground": "#359a25", "leading_diamond": " ", "style": "diamond", "template": "<#ca3c34></> {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }}", "trailing_diamond": " ", "type": "julia"}, {"background": "#ffffff", "foreground": "#9c1006", "leading_diamond": "", "style": "diamond", "template": " {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }}", "trailing_diamond": " ", "type": "ruby"}, {"background": "#ffffff", "foreground": "#5398c2", "leading_diamond": "", "style": "diamond", "template": "<#f5bf45></> {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }}", "trailing_diamond": " ", "type": "azfunc"}, {"background": "#565656", "foreground": "#faa029", "leading_diamond": "", "style": "diamond", "template": " {{.Profile}}{{if .Region}}@{{.Region}}{{end}}", "trailing_diamond": " ", "type": "aws"}, {"background": "#316ce4", "foreground": "#ffffff", "leading_diamond": "", "style": "diamond", "template": " {{.Context}}{{if .Namespace}} :: {{.Namespace}}{{end}}", "trailing_diamond": "", "type": "kubectl"}, {"background": "#b2bec3", "foreground": "#222222", "leading_diamond": "", "trailing_diamond": "<transparent,background></>", "properties": {"linux": "", "macos": "", "windows": ""}, "style": "diamond", "template": " {{ if .WSL }}WSL at {{ end }}{{.Icon}} ", "type": "os"}, {"background": "#f36943", "background_templates": ["{{if eq \"Charging\" .State.String}}#b8e994{{end}}", "{{if eq \"Discharging\" .State.String}}#fff34e{{end}}", "{{if eq \"Full\" .State.String}}#33DD2D{{end}}"], "foreground": "#262626", "invert_powerline": true, "powerline_symbol": "", "properties": {"charged_icon": " ", "charging_icon": " ", "discharging_icon": " "}, "style": "powerline", "template": " {{ if not .Error }}{{ .Icon }}{{ .Percentage }}{{ end }}{{ .Error }} ", "type": "battery"}, {"background": "#40c4ff", "foreground": "#ffffff", "invert_powerline": true, "leading_diamond": "", "properties": {"time_format": "_2,15:04"}, "style": "diamond", "template": "  {{ .CurrentDate | date .Format }} ", "trailing_diamond": "", "type": "time"}], "type": "prompt"}, {"alignment": "left", "newline": true, "segments": [{"foreground": "#21c7c7", "style": "plain", "template": "╰─", "type": "text"}, {"foreground": "#e0f8ff", "foreground_templates": ["{{ if gt .Code 0 }}#ef5350{{ end }}"], "properties": {"always_enabled": true}, "style": "plain", "template": " ", "type": "status"}], "type": "prompt"}], "version": 3}