{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "palette": {"os": "#ACB0BE", "closer": "p:os", "pink": "#ea76cb", "lavender": "#7287FD", "blue": "#1e66f5"}, "blocks": [{"alignment": "left", "segments": [{"foreground": "p:os", "style": "plain", "template": "{{.<PERSON><PERSON>}} ", "type": "os"}, {"foreground": "p:blue", "style": "plain", "template": "{{ .UserName }}@{{ .HostName }} ", "type": "session"}, {"foreground": "p:pink", "properties": {"folder_icon": "....", "home_icon": "~", "style": "agnoster_short"}, "style": "plain", "template": "{{ .Path }} ", "type": "path"}, {"foreground": "p:lavender", "properties": {"branch_icon": " ", "cherry_pick_icon": " ", "commit_icon": " ", "fetch_status": false, "fetch_upstream_icon": false, "merge_icon": " ", "no_commits_icon": " ", "rebase_icon": " ", "revert_icon": " ", "tag_icon": " "}, "template": "{{ .HEAD }} ", "style": "plain", "type": "git"}, {"style": "plain", "foreground": "p:closer", "template": "", "type": "text"}], "type": "prompt"}], "final_space": true, "version": 3}