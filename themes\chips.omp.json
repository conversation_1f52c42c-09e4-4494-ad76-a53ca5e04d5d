{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "segments": [{"background_templates": ["{{ if not (empty .Env.OVERRIDE_FOLDER_BADGE_BG) }}{{ .Env.OVERRIDE_FOLDER_BADGE_BG }}{{ else }}p:c-badge-folder{{ end }}"], "foreground_templates": ["{{ if not (empty .Env.OVERRIDE_FOLDER_BADGE_FG) }}{{ .Env.OVERRIDE_FOLDER_BADGE_FG }}{{ else }}p:c-badge-text{{ end }}"], "leading_diamond": "", "properties": {"style": "agnoster_short", "folder_separator_icon": "/", "hide_root_location": true, "max_depth": 2}, "style": "diamond", "template": " {{ .Path }}", "trailing_diamond": "", "type": "path"}, {"background": "p:c-git-normal", "background_templates": ["{{ if gt .Ahead 0 }}p:c-git-ahead{{ end }}", "{{ if gt .Behind 0 }}p:c-git-behind{{ end }}", "{{ if and (gt .Ahead 0) (gt .Behind 0) }}p:c-git-ahead-behind{{ end }}", "{{ if and (not .Working.Changed) (.Staging.Changed) }}p:c-git-staging{{ end }}", "{{ if and (.Working.Changed) (.Staging.Changed) }}p:c-git-staging-working{{ end }}", "{{ if .UpstreamGone }}p:c-git-upstream-gone{{ end }}", "{{ if and (.Working.Changed) (not .Staging.Changed) }}p:c-git-working{{ end }}"], "foreground": "p:c-badge-text", "leading_diamond": " ", "style": "diamond", "template": "{{ .HEAD }} {{ .BranchStatus }}{{ if .Working.Changed }}   ({{ .Working.String }}){{ end }}{{ if and .Working.Changed .Staging.Changed }}  {{ end }}{{ if .Staging.Changed }}{{ if not .Working.Changed }}  {{ end }} ({{ .Staging.String }}){{ end }}{{ if .StashCount }}  {{ .StashCount }} Stash{{ if gt .StashCount 1 }}es{{ end }}{{ end }}", "properties": {"branch_icon": " ", "fetch_status": true, "fetch_stash_count": true}, "trailing_diamond": "", "type": "git"}], "type": "prompt"}, {"alignment": "right", "segments": [{"background_templates": ["{{ if lt .Ms 60000 }}p:c-exec-fast{{ end }}", "{{ if lt .Ms 3600000 }}p:c-exec-normal{{ end }}", "{{ if lt .Ms 10800000 }}p:c-exec-slow{{ end }}", "{{ if ge .<PERSON> 10800000 }}p:c-exec-slower{{ end }}"], "foreground": "p:c-badge-text", "leading_diamond": "", "properties": {"style": "austin", "threshold": 1}, "style": "diamond", "template": "{{ if eq \"False\" (title (default \"False\" .Env.DISABLE_SEGMENT_PRIMARY_EXEC_TIME)) }}󰔟 {{ .FormattedMs }}.{{ end }}", "trailing_diamond": " ", "type": "executiontime"}, {"background_templates": ["{{ if lt .CumulativeTotal.Seconds 3600 }}p:c-wakatime-undertime{{ end }}", "{{ if lt .CumulativeTotal.Seconds 10800 }}p:c-wakatime-warm-up{{ end }}", "{{ if lt .CumulativeTotal.Seconds 25200 }}p:c-wakatime-working{{ end }}", "{{ if lt .CumulativeTotal.Seconds 28000 }}p:c-wakatime-quota{{ end }}", "{{ if ge .CumulativeTotal.Seconds 28800 }}p:c-wakatime-overtime{{ end }}"], "foreground": "p:c-badge-text", "leading_diamond": "", "properties": {"url": "https://wakatime.com/api/v1/users/current/summaries?start=today&end=today&api_key={{ .Env.WAKATIME_API_KEY }}", "http_timeout": 2000}, "style": "diamond", "template": "{{ if and (.Env.WAKATIME_API_KEY) (eq \"False\" (title (default \"False\" .Env.DISABLE_SEGMENT_WAKATIME))) (gt .CumulativeTotal.Seconds 0) }}神 {{ secondsRound .CumulativeTotal.Seconds }}.{{ end }}", "trailing_diamond": " ", "type": "wakatime", "cache": {"duration": "1minute", "strategy": "session"}}, {"background_templates": ["{{ if regexMatch \"(^(0[6-9])|(1[0-1])):\" (.CurrentDate | date .Format) }}p:c-date-time-morning{{ end }}", "{{ if regexMatch (\"(^1[2-4]):\") (.CurrentDate | date .Format) }}p:c-date-time-noon{{ end }}", "{{ if regexMatch (\"(^1[5-7]):\") (.CurrentDate | date .Format) }}p:c-date-time-afternoon{{ end }}", "{{ if regexMatch \"(^(1[8-9])|(2[0-3])):\" (.CurrentDate | date .Format) }}p:c-date-time-evening{{ end }}", "{{ if regexMatch \"^(0[0-5]):\" (.CurrentDate | date .Format) }}p:c-date-time-night{{ end }}"], "foreground": "p:c-badge-text", "leading_diamond": "", "properties": {"time_format": "15:04 (01/02)"}, "style": "diamond", "template": "{{ if eq \"False\" (title (default \"False\" .Env.DISABLE_SEGMENT_DTIME)) }} {{ .CurrentDate | date .Format }}{{ end }}", "trailing_diamond": " ", "type": "time"}, {"background_templates": ["{{ if .Error }}p:c-battery-state-error{{ end }}", "{{ if le .Percentage 15 }}p:c-battery-15-less{{ end }}", "{{ if and (ge .Percentage 16) (le .Percentage 30) }}p:c-battery-30-less{{ end }}", "{{ if and (ge .Percentage 31) (le .Percentage 45) }}p:c-battery-45-less{{ end }}", "{{ if and (ge .<PERSON>centage 46) (le .Percentage 55) }}p:c-battery-55-less{{ end }}", "{{ if and (ge .<PERSON> 56) (le .Percentage 70) }}p:c-battery-70-less{{ end }}", "{{ if and (ge .<PERSON> 71) (le .Percentage 90) }}p:c-battery-90-less{{ end }}", "{{ if and (ge .Percentage 91) (le .Percentage 100) }}p:c-battery-100-less{{ end }}"], "foreground": "p:c-badge-text", "leading_diamond": "", "style": "diamond", "template": "{{ if eq \"True\" (title (default \"False\" .Env.DISABLE_SEGMENT_BATTERY)) }}{{ else }}{{ if not .Error }}{{ if eq \"Charging\" .State.String }} {{ else if eq \"Discharging\" .State.String }}{{ else if eq \"Full\" .State.String }}~ {{ else }}? {{ end }}{{ if le .Percentage 15 }}󰁺{{ else if and (ge .Percentage 16) (le .Percentage 30) }}󰁻{{ else if and (ge .Percentage 31) (le .Per<PERSON> 45) }}󰁽{{ else if and (ge .Percentage 46) (le .Percentage 55)}}󰁾{{ else if and (ge .Percentage 56) (le .<PERSON> 70) }}󰁿{{ else if and (ge .Percentage 71) (le .<PERSON> 80) }}󰂂{{ else if and (ge .Percentage 81) (le .<PERSON> 95) }}󰂂{{ else }}󰁹{{ end }} {{ .Percentage }}%{{ else }}!{{ end }}{{ end }}", "trailing_diamond": "", "type": "battery"}], "type": "prompt"}, {"alignment": "left", "newline": true, "segments": [{"template": "{{ if false }}{{ end }}", "type": "session", "style": "diamond"}, {"background_templates": ["{{ if and (.Segments.Session.SSHSession) (not .Root) }}p:c-shell-state-ssh-active{{ end }}", "{{ if and (not .Segments.Session.SSHSession) (.Root) }}p:c-shell-state-root-active{{ end }}", "{{ if and (.Segments.Session.SSHSession) (.Root) }}p:c-shell-state-root-ssh-active{{ end }}"], "foreground": "p:c-badge-text", "leading_diamond": "", "style": "diamond", "template": "{{ if or (.Segments.Session.SSHSession) (.Root) }}{{ if .Segments.Session.SSHSession }} SSH{{ end }}{{ if and (.Segments.Session.SSHSession) (.Root) }}  {{ end }}{{ if .Root }}# Root{{ end }}{{ end }}", "trailing_diamond": " ", "type": "text"}, {"background_templates": ["{{ if empty .Full }}p:c-project-generic-error{{ else }}p:c-project-crystal{{ end }}"], "foreground": "p:c-badge-text", "leading_diamond": "", "style": "diamond", "template": "{{ if eq \"False\" (title (default \"False\" .Env.DISABLE_SEGMENT_PROJECT_CRYSTAL)) }} {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }}{{ end }}", "trailing_diamond": " ", "type": "crystal"}, {"background_templates": ["{{ if empty .Full }}p:c-project-generic-error{{ else }}p:c-project-flutter{{ end }}"], "foreground": "p:c-badge-text", "leading_diamond": "", "style": "diamond", "template": "{{ if eq \"False\" (title (default \"False\" .Env.DISABLE_SEGMENT_PROJECT_FLUTTER)) }}Flutter | {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }}{{ end }}", "trailing_diamond": " ", "type": "dart"}, {"background_templates": ["{{ if .Error }}p:c-project-generic-error{{ else }}p:c-project-lua{{ end }}"], "foreground": "p:c-badge-text", "leading_diamond": "", "style": "diamond", "template": "{{ if eq \"False\" (title (default \"False\" .Env.DISABLE_SEGMENT_PROJECT_LUA)) }} {{ if or (.Error) (empty .Full) }}{{ .Error }}{{ else }}{{ .Full }}{{ end }}{{ end }}", "trailing_diamond": " ", "type": "lua"}, {"background_templates": ["{{ if empty .Full }}p:c-project-generic-error{{ else }}p:c-project-node{{ end }}"], "foreground": "p:c-badge-text", "leading_diamond": "", "style": "diamond", "template": "{{ if eq \"False\" (title (default \"False\" .Env.DISABLE_SEGMENT_PROJECT_NODE)) }} {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }}{{ end }}", "trailing_diamond": " ", "type": "node"}, {"background_templates": ["{{ if empty .Full }}p:c-project-generic-error{{ else }}p:c-project-rust{{ end }}"], "foreground": "p:c-badge-text", "leading_diamond": "", "style": "diamond", "template": "{{ if eq \"False\" (title (default \"False\" .Env.DISABLE_SEGMENT_PROJECT_RUST)) }} {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }}{{ end }}", "trailing_diamond": " ", "type": "rust"}, {"background_templates": ["{{ if empty .Full }}p:c-project-generic-error{{ else }}p:c-project-python{{ end }}"], "foreground": "p:c-badge-text", "leading_diamond": "", "properties": {"display_mode": "context"}, "style": "diamond", "template": "{{ if or (eq \"False\" (title (default \"False\" .Env.DISABLE_SEGMENT_PROJECT_PYTHON))) (eq \"False\" (title (default \"False\" .Env.DISABLE_SEGMENT_PROJECT_PYTHON_VENV))) }} {{ if eq \"False\" (title (default \"False\" .Env.DISABLE_SEGMENT_PROJECT_PYTHON)) }}{{ .Full }}{{ end }}{{ if and (.Venv) (eq \"False\" (title (default \"False\" .Env.DISABLE_SEGMENT_PROJECT_PYTHON))) (eq \"False\" (title (default \"False\" .Env.DISABLE_SEGMENT_PROJECT_PYTHON_VENV))) }}  {{ end }}{{ if and (.Venv) (eq \"False\" (title (default \"False\" .Env.DISABLE_SEGMENT_PROJECT_PYTHON_VENV))) }}{{ if .Env.SEGMENT_PROJECT_PYTHON_ACTIVE_VENV_STR }}{{ .Env.SEGMENT_PROJECT_PYTHON_ACTIVE_VENV_STR }}{{ else }}{{ .Venv }}{{ end }}{{ end }}{{ end }}", "trailing_diamond": " ", "type": "python"}, {"style": "plain", "foreground_templates": ["{{ if eq \"False\" (title (default \"False\" .Env.ENABLE_ARROW_DIVIDER_COLOR_EXECUTION_RETURN)) }}p:c-badge-white{{ else }}{{ if eq .Code 0 }}p:c-badge-return-success{{ else if or (eq .Code 1) (eq .Code 130) }}p:c-badge-return-fail-term{{ else }}p:c-badge-return-custom{{ end }}{{ end }}"], "template": "❯", "type": "text"}], "type": "prompt"}], "console_title_template": "{{ if .Segments.Session.SSHSession }}SSH'd{{ if or .Root }} & {{ end }}{{ end }}{{ if .Root }}# (as {{ .UserName }}) | {{ end }}{{ if .WSL }}WSL | {{ end }}{{ .Folder }} ({{ .Shell }})", "final_space": true, "palette": {"c-badge-folder": "#FFD770", "c-badge-text": "#212121", "c-badge-white": "#FAFAFA", "c-badge-return-custom": "#E7B9FF", "c-badge-return-fail-term": "#FF8A80", "c-badge-return-success": "#B2FF59", "c-battery-15-less": "#FF8A80", "c-battery-30-less": "#FFD180", "c-battery-45-less": "#FFE57F", "c-battery-55-less": "#FFFF8D", "c-battery-70-less": "#F4FF81", "c-battery-90-less": "#B9F6CA", "c-battery-100-less": "#CCFF90", "c-battery-state-error": "#FF867F", "c-date-time-morning": "#FFFF8D", "c-date-time-noon": "#FFF64F", "c-date-time-afternoon": "#FFC400", "c-date-time-evening": "#C0CFFF", "c-date-time-night": "#83B9FF", "c-exec-fast": "#C6FF00", "c-exec-normal": "#FFFF00", "c-exec-slow": "#FFD180", "c-exec-slower": "#FF867F", "c-git-ahead": "#6EFFFF", "c-git-behind": "#FFA06D", "c-git-ahead-behind": "#C0CFFF", "c-git-normal": "#66FFA6", "c-git-staging": "#FFD740", "c-git-staging-working": "#FFB2FF", "c-git-upstream-gone": "#FF867F", "c-git-working": "#84FFFF", "c-project-generic-error": "#FF867F", "c-project-crystal": "#FFFFFF", "c-project-flutter": "#6DC2FF", "c-project-lua": "#BBC2FF", "c-project-node": "#9CFF57", "c-project-rust": "#FFAB40", "c-project-python": "#FFE873", "c-secondary-ellipsis": "#FFFF8D", "c-shell-state-ssh-active": "#BAFFFF", "c-shell-state-root-active": "#9FFFE0", "c-shell-state-root-ssh-active": "#FFB2FF", "c-wakatime-undertime": "#A7FFEB", "c-wakatime-warm-up": "#FFFFB3", "c-wakatime-working": "#FFD180", "c-wakatime-quota": "#FFD0B0", "c-wakatime-overtime": "#FF8A80"}, "transient_prompt": {"template": "{{ if eq \"False\" (title (default \"False\" .Env.DISABLE_SEGMENT_TRANSIENT)) }}<{{ if eq .Code 0 }}p:c-badge-return-success{{ else if or (eq .Code 1) (eq .Code 130) }}p:c-badge-return-fail-term{{ else }}p:c-badge-return-custom{{ end }}></><p:c-badge-text,{{ if eq .Code 0 }}p:c-badge-return-success{{ else if or (eq .Code 1) (eq .Code 130) }}p:c-badge-return-fail-term{{ else }}p:c-badge-return-custom{{ end }}>󰁞  {{ if .Segments.Executiontime.Ms }}{{ if eq \"False\" (title (default \"False\" .Env.DISABLE_SEGMENT_TRANSIENT_EXEC_TIME)) }}󰔟 {{ .Segments.Executiontime.FormattedMs }}  {{ end }}{{ end }}<b>{{ if eq .Code 0 }}OK{{ else if eq .Code 1 }}FAIL{{ else if eq .Code 130 }}TERM{{ else }}Code{{ end }} ({{ .Code }})</b></><{{ if eq .Code 0 }}p:c-badge-return-success{{ else if or (eq .Code 1) (eq .Code 130) }}p:c-badge-return-fail-term{{ else }}p:c-badge-return-custom{{ end }}></> {{ end }}❯ "}, "secondary_prompt": {"background": "transparent", "foreground": "p:c-secondary-ellipsis", "template": " ... "}, "version": 3}