{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "segments": [{"background": "#07585c", "foreground": "#ffffff", "leading_diamond": "", "style": "diamond", "template": " {{ .UserName }}@{{ .HostName }} ", "type": "session"}, {"background": "#3e9022", "foreground": "#ffffff", "properties": {"style": "folder"}, "style": "plain", "template": "  {{ .Path }} ", "type": "path"}, {"background": "#de076f", "foreground": "#ffffff", "properties": {"branch_icon": "", "fetch_upstream_icon": true}, "style": "plain", "template": " {{ .UpstreamIcon }}{{ .HEAD }} ", "type": "git"}, {"background": "#491545", "foreground": "#ffffff", "properties": {"paused_icon": " ", "playing_icon": " ", "stopped_icon": " ", "track_separator": " - "}, "style": "plain", "template": "  {{ .Icon }}{{ if ne .Status \"stopped\" }}{{ .Artist }} - {{ .Track }}{{ end }} ", "type": "spotify"}, {"background": "#491515", "background_templates": ["{{ if gt .Code 0 }}#f1184c{{ end }}"], "foreground": "#ffffff", "properties": {"always_enabled": true}, "style": "diamond", "template": "  ", "trailing_diamond": "", "type": "status"}], "type": "prompt"}], "final_space": true, "version": 3}