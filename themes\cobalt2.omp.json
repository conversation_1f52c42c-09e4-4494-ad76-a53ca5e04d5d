{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "segments": [{"background": "#1478DB", "foreground": "#000000", "leading_diamond": "", "trailing_diamond": "", "properties": {"style": "full"}, "style": "diamond", "template": "{{ .Path }} ", "type": "path"}, {"background": "#3AD900", "background_templates": ["{{ if or (.Working.Changed) (.Staging.Changed) }}#FFC600{{ end }}", "{{ if and (gt .Ahead 0) (gt .Behind 0) }}#FFCC80{{ end }}", "{{ if gt .Ahead 0 }}#B388FF{{ end }}", "{{ if gt .Behind 0 }}#B388FF{{ end }}"], "foreground": "#000000", "leading_diamond": "<transparent,background></>", "trailing_diamond": "", "properties": {"fetch_stash_count": true, "fetch_status": true}, "style": "diamond", "template": " {{ .HEAD }}{{ if .Staging.Changed }}<#FF6F00>  {{ .Staging.String }}</>{{ end }}{{ if and (.Working.Changed) (.Staging.Changed) }} |{{ end }}{{ if .Working.Changed }}  {{ .Working.String }}{{ end }}{{ if gt .StashCount 0 }}  {{ .StashCount }}{{ end }} ", "type": "git"}], "type": "prompt"}], "final_space": true, "version": 3}