{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "segments": [{"foreground": "#FFBD00", "style": "plain", "template": "{{ .<PERSON>r<PERSON><PERSON> }}'s ", "type": "session"}, {"foreground": "#00C6F7", "properties": {"style": "folder"}, "style": "plain", "template": "{{ .Path }}/ ", "type": "path"}, {"foreground": "#F62F2E", "properties": {"fetch_status": true}, "style": "plain", "template": "<#ffffff>on</> {{ .HEAD }}{{if .BranchStatus }} {{ .BranchStatus }}{{ end }}{{ if .Working.Changed }}  {{ .Working.String }}{{ end }}{{ if and (.Working.Changed) (.Staging.Changed) }} |{{ end }}{{ if .Staging.Changed }}  {{ .Staging.String }}{{ end }} ", "type": "git"}, {"foreground": "#98C379", "properties": {"fetch_version": true}, "style": "plain", "template": "<#ffffff>via</>  {{ if .PackageManagerIcon }}{{ .PackageManagerIcon }} {{ end }}{{ .Full }} ", "type": "node"}], "type": "prompt"}, {"alignment": "right", "segments": [{"foreground": "#85C980", "style": "diamond", "template": "RAM:{{ (div ((sub .PhysicalTotalMemory .PhysicalAvailableMemory)|float64) 1073741824.0) }}/{{ (div .PhysicalTotalMemory 1073741824.0) }}GB ", "trailing_diamond": " ", "type": "sysinfo"}, {"foreground": "#ffffff", "leading_diamond": "┋", "properties": {"style": "roundrock", "threshold": 0}, "style": "diamond", "template": " {{ .FormattedMs }} ", "type": "executiontime"}], "type": "prompt"}, {"alignment": "left", "newline": true, "segments": [{"foreground": "#FFBD00", "style": "plain", "template": "⮞ ", "type": "text"}], "type": "prompt"}], "final_space": true, "version": 3}