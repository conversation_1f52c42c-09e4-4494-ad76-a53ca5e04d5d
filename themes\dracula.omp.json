{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "segments": [{"background": "#6272a4", "foreground": "#f8f8f2", "leading_diamond": "", "style": "diamond", "template": "{{ .<PERSON>r<PERSON><PERSON> }} ", "type": "session"}, {"background": "#bd93f9", "foreground": "#f8f8f2", "powerline_symbol": "", "properties": {"style": "folder"}, "style": "powerline", "template": " {{ .Path }} ", "type": "path"}, {"background": "#ffb86c", "foreground": "#f8f8f2", "powerline_symbol": "", "properties": {"branch_icon": "", "fetch_stash_count": true, "fetch_status": false, "fetch_upstream_icon": true}, "style": "powerline", "template": "  ({{ .UpstreamIcon }}{{ .HEAD }}{{ if gt .StashCount 0 }}  {{ .StashCount }}{{ end }}) ", "type": "git"}, {"background": "#8be9fd", "foreground": "#f8f8f2", "powerline_symbol": "", "style": "powerline", "template": "  {{ if .PackageManagerIcon }}{{ .PackageManagerIcon }} {{ end }}{{ .Full }} ", "type": "node"}, {"background": "#ff79c6", "foreground": "#f8f8f2", "properties": {"time_format": "15:04"}, "style": "diamond", "template": " ♥ {{ .CurrentDate | date .Format }} ", "trailing_diamond": "", "type": "time"}], "type": "prompt"}, {"alignment": "left", "segments": [{"background": "#f1fa8c", "foreground": "#282a36", "invert_powerline": true, "leading_diamond": "", "style": "diamond", "template": "  {{.Profile}}{{if .Region}}@{{.Region}}{{end}}", "trailing_diamond": "", "type": "aws"}], "type": "rprompt"}], "final_space": true, "version": 3}