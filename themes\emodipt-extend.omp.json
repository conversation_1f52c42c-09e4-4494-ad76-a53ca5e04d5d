{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "newline": true, "segments": [{"foreground": "#E5C07B", "properties": {"time_format": "15:04"}, "style": "plain", "template": "[{{ .CurrentDate | date .Format }}]", "type": "time"}, {"type": "shell", "style": "plain", "foreground": "#E06C75", "properties": {"mapped_shell_names": {"pwsh": "Shell", "powershell": "Shell", "cmd": "Cmd", "bash": "<PERSON><PERSON>"}}, "template": "  {{ .Name }}"}, {"foreground": "#F3C267", "foreground_templates": ["{{ if or (.Working.Changed) (.Staging.Changed) }}#FF9248{{ end }}", "{{ if and (gt .Ahead 0) (gt .Behind 0) }}#ff4500{{ end }}", "{{ if gt .Ahead 0 }}#B388FF{{ end }}", "{{ if gt .Behind 0 }}#B388FF{{ end }}"], "properties": {"branch_template": "{{ trunc 25 .<PERSON> }}", "fetch_stash_count": true, "fetch_status": true, "fetch_upstream_icon": true}, "style": "plain", "template": " {{ .UpstreamIcon }}{{ .HEAD }}{{if .BranchStatus }} {{ .BranchStatus }}{{ end }}{{ if .Working.Changed }}  {{ .Working.String }}{{ end }}{{ if and (.Working.Changed) (.Staging.Changed) }} |{{ end }}{{ if .Staging.Changed }}  {{ .Staging.String }}{{ end }}{{ if gt .StashCount 0 }}  {{ .StashCount }}{{ end }} ", "type": "git"}], "type": "prompt"}, {"alignment": "right", "segments": [{"type": "status", "style": "plain", "foreground": "#b8ff75", "foreground_templates": ["{{ if gt .Code 0 }}#E06C75{{ end }}"], "template": " x{{ reason .Code }}"}, {"foreground": "#b8ff75", "foreground_templates": ["{{ if gt .Code 0 }}#E06C75{{ end }}"], "properties": {"style": "roundrock", "always_enabled": true}, "style": "diamond", "template": " {{ .FormattedMs }} ", "type": "executiontime"}], "type": "prompt"}, {"alignment": "left", "newline": true, "segments": [{"foreground": "#61AFEF", "properties": {"style": "full"}, "style": "plain", "template": " {{ .Path }}", "type": "path"}], "type": "prompt"}, {"alignment": "left", "newline": true, "segments": [{"foreground": "#E06C75", "style": "plain", "template": "!", "type": "root"}, {"foreground": "#E06C75", "style": "plain", "template": "❯", "type": "text"}], "type": "prompt"}], "final_space": true, "version": 3}