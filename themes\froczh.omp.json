{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "segments": [{"background": "#40189c", "foreground": "#ffffff", "leading_diamond": "", "style": "diamond", "template": "{{ if .WSL }}WSL at {{ end }}{{.Icon}} ", "type": "os"}, {"background": "#40189c", "foreground": "#ffffff", "style": "diamond", "template": "| {{ .UserName }}@{{ .HostName }} ", "type": "session", "trailing_diamond": ""}, {"background": "#01579B", "foreground": "#ffffff", "leading_diamond": "<transparent,#01579B></>", "properties": {"folder_icon": "...", "folder_separator_icon": "<transparent>  </>", "home_icon": "", "style": "agnoster_short"}, "style": "diamond", "template": " {{ .Path }} ", "trailing_diamond": "", "type": "path"}, {"background": "#00C853", "background_templates": ["{{ if or (.Working.Changed) (.Staging.Changed) }}#FFEB3B{{ end }}", "{{ if and (gt .Ahead 0) (gt .Behind 0) }}#FFCC80{{ end }}", "{{ if gt .Ahead 0 }}#B388FF{{ end }}", "{{ if gt .Behind 0 }}#B388FF{{ end }}"], "foreground": "#000000", "powerline_symbol": "", "properties": {"fetch_stash_count": true, "fetch_status": true}, "style": "powerline", "template": " {{ .HEAD }}{{ if .Staging.Changed }}<#FF6F00>  {{ .Staging.String }}</>{{ end }}{{ if and (.Working.Changed) (.Staging.Changed) }} |{{ end }}{{ if .Working.Changed }}  {{ .Working.String }}{{ end }}{{ if gt .StashCount 0 }}  {{ .StashCount }}{{ end }} ", "type": "git"}, {"type": "dotnet", "style": "powerline", "powerline_symbol": "", "foreground": "#000000", "background": "#00ffff", "template": "  {{ .Full }} "}, {"type": "angular", "style": "powerline", "powerline_symbol": "", "background": "#000000", "foreground": "#ffffff", "template": "  {{ if .Error }}<#FE4A49>?</>{{ else }}{{ .Full }}{{ end }} "}, {"type": "aurelia", "style": "powerline", "powerline_symbol": "", "background": "#000000", "foreground": "#ffffff", "template": "  {{ if .Error }}<#DE1F84>?</>{{ else }}{{ .Full }}{{ end }} "}, {"type": "node", "style": "powerline", "powerline_symbol": "", "background": "#000000", "foreground": "#45bf17", "template": "  {{ .Full }} "}, {"type": "npm", "style": "powerline", "powerline_symbol": "", "background": "#000000", "foreground": "#FE4A49", "template": " {{ .Full }} "}, {"background": "#910000", "foreground": "#ffffff", "powerline_symbol": "", "style": "powerline", "template": "<transparent> </> {{ reason .Code }} ", "type": "status"}], "type": "prompt"}, {"alignment": "right", "segments": [{"background": "#29315A", "foreground": "#ffffff", "leading_diamond": "", "properties": {"style": "dallas", "threshold": 0}, "style": "diamond", "template": " {{ .FormattedMs }}s ", "type": "executiontime"}, {"background": "#29315A", "foreground": "#3EC669", "properties": {"time_format": "15:04:05 | Monday"}, "style": "plain", "template": "<transparent>  </>{{ .CurrentDate | date .Format }} ", "type": "time"}], "type": "prompt"}, {"alignment": "left", "newline": true, "segments": [{"foreground": "#ffffff", "foreground_templates": ["{{ if gt .Code 0 }}#ff0000{{ end }}"], "properties": {"always_enabled": true}, "style": "plain", "template": "❯ ", "type": "status"}], "type": "prompt"}], "console_title_template": "{{if .Root}} ⚡ {{end}}{{.Folder | replace \"~\" \"🏚\" }} @ {{.HostName}}", "version": 3}