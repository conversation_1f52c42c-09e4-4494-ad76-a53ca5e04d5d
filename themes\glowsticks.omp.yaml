# yaml-language-server: $schema=https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json
blocks:
  - alignment: left
    segments:
      # Session
      - background: p:black
        foreground: p:white
        invert_powerline: true
        powerline_symbol: 
        style: powerline
        template: "{{ .UserName }}@{{ .HostName }} "
        type: session

      # Path
      - background: p:orange
        foreground: p:white
        powerline_symbol: 
        properties:
          home_icon: ""
          style: folder
        style: powerline
        template: "  {{ .Path }} "
        type: path

      # Git
      - background: p:green
        foreground: p:white
        powerline_symbol: 
        properties:
          script: |
            BRANCH=$(git branch --show-current 2>/dev/null)
            [[ -z $BRANCH ]] && exit
            status=$(git status --porcelain | awk '{print $1}')
            [[ $status == *"R"* ]] && bits+='>'
            [[ $status == *"A"* ]] && bits+='+'
            [[ $status == *"??"* ]] && bits+='?'
            [[ $status == *"D"* ]] && bits+='x'
            [[ $status == *"M"* ]] && bits+='!'
            [[ "$(git rev-list --count "@{u}..")" -gt 0 ]] && bits+='*'
            [[ -n $bits ]] && STAT="  $bits"
            echo -n " $BRANCH$STAT"
          shell: bash
        style: powerline
        template: " {{ .Output }} "
        type: command

      # Execution time
      - background: p:darkred
        foreground: p:white
        powerline_symbol: 
        properties:
          always_enabled: true
          style: round
        style: powerline
        template: " 󰔟{{ .FormattedMs }} "
        type: executiontime

      # Exit code
      - background: p:blue
        background_templates:
          - "{{ if gt .Code 0 }}p:red{{ end }}"
        foreground: p:white
        powerline_symbol: 
        properties:
          always_enabled: true
        style: diamond
        template: " {{ if gt .Code 0 }} {{ reason .Code }}{{ else }}{{ end }} "
        trailing_diamond: 
        type: status
    type: prompt
  - alignment: right
    segments:
      # Shell
      - background: p:black
        foreground: p:white
        style: plain
        template: <p:black,transparent></> {{ .Name }}<transparent,p:black></>
        type: shell

      # Battery
      - background_templates:
          - '{{if eq "Charging" .State.String }}p:green{{end}}'
          - '{{if eq "Discharging" .State.String}}p:orange{{end}}'
        foreground: p:white
        leading_diamond: 
        properties:
          charged_icon: 󰂄
          charging_icon: 󰂄
          discharging_icon: "󰁹 "
        style: diamond
        template: " {{.Templates }}{{ .Percentage}}󰏰 <transparent></>"
        templates:
          - '{{if eq "Discharging" .State.String}}{{if lt .Percentage 11}}󰁺{{else if lt .Percentage 21}}󰁻{{else if lt .Percentage 31}}󰁼{{else if lt .Percentage 41}}󰁽{{else if lt .Percentage 51}}󰁾{{else if lt .Percentage 61}}󰁾{{else if lt .Percentage 71}}󰂀{{else if lt .Percentage 81}}󰂁{{else if lt .Percentage 91}}󰂂{{else}}󰁹{{end}}{{end}}'
          - '{{if eq "Charging" .State.String}}{{ if lt .Percentage 21}}󰂆{{else if lt .Percentage 31}}󰂇{{else if lt .Percentage 41}}󰂈{{else if lt .Percentage 61}}󰂉{{else if lt .Percentage 81}}󰂊{{else if lt .Percentage 91}}󰂋{{else}}󰂄{{end}}{{end}}'
        templates_logic: first_match
        type: battery

      # Weather & time of day
      - background: p:blue
        foreground: p:white
        leading_diamond: 
        properties:
          script: |
            now="$(date +'%I:%M%P')"
            if [[ -f /tmp/weather.w ]]; then
              update="$(cat /tmp/last_update.w)"
              if [[ -f /tmp/w_lock.w || $(date +'%s') -le $((update + 600)) ]]; then
                echo -n "$now"
                cat /tmp/weather.w
                exit 0
              fi
            elif [[ -f /tmp/w_lock.w ]]; then
              echo "$now"
              exit 0
            fi
            touch /tmp/w_lock.w
            if ! res=$(curl -s wttr.in -Gsm2 --data-urlencode "format=j1"); then
              echo "$now"
              date +'%s' >/tmp/last_update.w
              echo 󰖪 >/tmp/weather.w
              rm /tmp/w_lock.w
              exit 0
            fi
            res=$(jq .current_condition[] -c <<<"$res")
            temp=$(jq -r .temp_C <<<"$res")
            feels_like=$(jq -r .FeelsLikeC <<<"$res")
            [[ $(bc -l <<<"$feels_like!=$temp") -eq 1 ]] && feels_string=" feels $feels_like°C"
            WWC_MAP='{"113":"Sunny","116":"PartlyCloudy","119":"Cloudy","122":"VeryCloudy","143":"Fog","176":"LightShowers","179":"LightSleetShowers","182":"LightSleet","185":"LightSleet","200":"ThunderyShowers","227":"LightSnow","230":"HeavySnow","248":"Fog","260":"Fog","263":"LightShowers","266":"LightRain","281":"LightSleet","284":"LightSleet","293":"LightRain","296":"LightRain","299":"HeavyShowers","302":"HeavyRain","305":"HeavyShowers","308":"HeavyRain","311":"LightSleet","314":"LightSleet","317":"LightSleet","320":"LightSnow","323":"LightSnowShowers","326":"LightSnowShowers","329":"HeavySnow","332":"HeavySnow","335":"HeavySnowShowers","338":"HeavySnow","350":"LightSleet","353":"LightShowers","356":"HeavyShowers","359":"HeavyRain","362":"LightSleetShowers","365":"LightSleetShowers","368":"LightSnowShowers","371":"HeavySnowShowers","374":"LightSleetShowers","377":"LightSleet","386":"ThunderyShowers","389":"ThunderyHeavyRain","392":"ThunderySnowShowers","395":"HeavySnowShowers"}';NF_DAY_MAP='{"Unknown":"󰚌","Cloudy":"","Fog":"","HeavyRain":"","HeavyShowers":"","HeavySnow":"","HeavySnowShowers":"","LightRain":"","LightShowers":"","LightSleet":"","LightSleetShowers":"","LightSnow":"","LightSnowShowers":"","PartlyCloudy":"","Sunny":"","ThunderyHeavyRain":"","ThunderyShowers":"","ThunderySnowShowers":"","VeryCloudy":""}';NF_NIGHT_MAP='{"Unknown":"󰚌","Cloudy":"","Fog":"","HeavyRain":"","HeavyShowers":"","HeavySnow":"","HeavySnowShowers":"","LightRain":"","LightShowers":"","LightSleet":"","LightSleetShowers":"","LightSnow":"","LightSnowShowers":"","PartlyCloudy":"","Sunny":"","ThunderyHeavyRain":"","ThunderyShowers":"","ThunderySnowShowers":"","VeryCloudy":""}'
            cur_h="$(date +'%H')" ;wwc="$(jq '.weatherCode' <<<"$res")"
            if [[ $cur_h -gt 18 || $cur_h -lt 6 ]]; then icon=$(jq -r ".$(echo "$WWC_MAP" | jq -r ".$wwc")" <<<"$NF_DAY_MAP")
            else icon=$(jq -r ".$(echo "$WWC_MAP" | jq -r ".$wwc")" <<<"$NF_NIGHT_MAP"); fi
            weather="$icon  $temp°C$feels_string"
            echo "$now $weather"
            date +'%s' >/tmp/last_update.w
            echo "$weather" >/tmp/weather.w
            rm /tmp/w_lock.w
        style: diamond
        template: "{{ .Output }}"
        trailing_diamond: 
        type: command
    type: rprompt
final_space: true
console_title_template:  {{ .Shell }} in {{ if ne .Folder "~"}} {{ .Folder }}{{else}}{{end}}
palette:
  black: "#33395b"
  blue: "#359ddd"
  darkred: "#7c0a16"
  green: "#277b48"
  orange: "#ff972e"
  red: "#de3142"
  white: "#efeef4"
secondary_prompt:
  background: transparent
  foreground: p:white
  template: " "
transient_prompt:
  background: transparent
  foreground: p:white
  template: " "
version: 3
