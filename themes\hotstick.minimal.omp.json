{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "segments": [{"foreground": "yellow", "style": "plain", "template": "  ", "type": "root"}, {"background": "lightBlue", "foreground": "black", "leading_diamond": "", "properties": {"style": "mixed"}, "style": "diamond", "template": " {{ .Path }} ", "trailing_diamond": "", "type": "path"}, {"background": "green", "background_templates": ["{{ if or (.Working.Changed) (.Staging.Changed) }}yellow{{ end }}"], "foreground": "black", "powerline_symbol": "", "properties": {"branch_ahead_icon": "↑", "branch_behind_icon": "↓", "branch_gone": "≢", "branch_icon": " ", "branch_identical_icon": "≡", "cherry_pick_icon": "✓ ", "commit_icon": "▷ ", "fetch_status": true, "merge_icon": "◴ ", "no_commits_icon": "[no commits]", "rebase_icon": "Ɫ ", "tag_icon": "▶ "}, "style": "powerline", "template": " {{ .HEAD }}{{ if .Staging.Changed }} {{ .Staging.String }}{{ end }}{{ if and (.Working.Changed) (.Staging.Changed) }} |{{ end }}{{ if .Working.Changed }} {{ .Working.String }}{{ end }} ", "type": "git"}], "type": "prompt"}], "console_title_template": "{{.Folder}}{{if .Root}} :: root{{end}} :: {{.Shell}}", "final_space": true, "version": 3}