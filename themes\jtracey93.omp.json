{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "segments": [{"background": "#EB9654", "foreground": "#ffffff", "leading_diamond": "", "properties": {"display_host": false}, "style": "diamond", "template": "{{ if .SSHSession }} {{ end }}{{ .UserName }} ", "type": "session"}, {"background": "light<PERSON>ellow", "foreground": "#3f3f3f", "properties": {"style": "full"}, "style": "plain", "template": " {{ .Path }} ", "type": "path"}, {"background": "#25AFF3", "foreground": "#ffffff", "properties": {"fetch_status": true}, "style": "plain", "template": " branch ({{ .HEAD }}{{ if .Staging.Changed }}  {{ .Staging.String }}{{ end }}{{ if and (.Working.Changed) (.Staging.Changed) }} |{{ end }}{{ if .Working.Changed }}  {{ .Working.String }}{{ end }}) ", "type": "git"}, {"background": "#1BD760", "foreground": "#ffffff", "properties": {"paused_icon": "", "playing_icon": "", "stopped_icon": "", "track_separator": " - "}, "style": "plain", "template": " {{ .Icon }}{{ if ne .Status \"stopped\" }}{{ .Artist }} - {{ .Track }}{{ end }} ", "type": "spotify"}, {"background": "#cc00ff", "foreground": "#ffffff", "properties": {"time_format": "15:04:05"}, "style": "plain", "template": " {{ .CurrentDate | date .Format }} ", "type": "time"}, {"background": "#49404f", "foreground": "#ffffff", "properties": {"style": "dallas", "threshold": 0}, "style": "plain", "template": " {{ .FormattedMs }}s ", "type": "executiontime"}, {"type": "status", "style": "diamond", "foreground": "#ffffff", "background": "#00897b", "background_templates": ["{{ if .Error }}#e91e63{{ end }}"], "trailing_diamond": "", "properties": {"always_enabled": true}}], "type": "prompt"}, {"type": "prompt", "newline": true, "alignment": "left", "segments": [{"foreground": "#ffffff", "foreground_templates": ["{{ if gt .Code 0 }}#ff0000{{ end }}"], "properties": {"always_enabled": true}, "style": "plain", "template": "❯ ", "type": "status"}]}], "final_space": true, "version": 3}