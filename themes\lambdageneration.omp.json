{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "segments": [{"background": "#292929", "foreground": "#fb7e14", "leading_diamond": "╭─", "style": "diamond", "template": " {{ if .WSL }}WSL at {{ end }}{{.Icon}} ", "type": "os"}, {"background": "#292929", "foreground": "#fb7e14", "properties": {"always_enabled": true, "style": "austin", "threshold": 500}, "style": "powerline", "template": " {{ .FormattedMs }} ", "type": "executiontime"}, {"background": "#292929", "foreground": "#26C6DA", "style": "plain", "template": "  ", "type": "root"}, {"background": "#292929", "foreground": "#fb7e14", "properties": {"style": "full", "trailing_diamond": ""}, "style": "diamond", "template": " {{ .Path }} ", "type": "path"}, {"background": "#292929", "foreground": "#D4E157", "style": "plain", "type": "git"}, {"background": "#292929", "foreground": "#26C6DA", "foreground_templates": ["{{ if eq \"Full\" .State.String }}#292929{{ end }}", "{{ if eq \"Charging\" .State.String }}#40c4ff{{ end }}", "{{ if eq \"Discharging\" .State.String }}#fb7e14{{ end }}"], "properties": {"charged_icon": " ", "charging_icon": " ", "discharging_icon": " ", "text": ""}, "style": "powerline", "template": " {{ if not .Error }}{{ .Icon }}{{ .Percentage }}{{ end }}{{ .Error }} ", "type": "battery"}, {"background": "#292929", "background_templates": ["{{ if gt .Code 0 }}#292929{{ end }}"], "foreground": "#fb7e14", "properties": {"always_enabled": true, "display_exit_code": true}, "style": "diamond", "template": " {{ if gt .Code 0 }} {{ reason .Code }}{{ else }}{{ end }} ", "trailing_diamond": "", "type": "status"}], "type": "prompt"}, {"alignment": "right", "segments": [{"background": "#292929", "foreground": "#fb7e14", "leading_diamond": "", "style": "diamond", "template": " {{ if .SSHSession }} {{ end }}{{ .UserName }}@{{ .HostName }} <#fb7e14></> ", "type": "session"}, {"background": "#292929", "foreground": "#fb7e14", "properties": {"time_format": "15:04:05, _2"}, "style": "diamond", "template": "{{ .CurrentDate | date .Format }}  ", "trailing_diamond": "", "type": "time"}], "type": "prompt"}, {"alignment": "left", "newline": true, "segments": [{"properties": {"always_enabled": true}, "background": "#fb7e14", "foreground": "#292929", "style": "diamond", "leading_diamond": "╰─", "template": "", "trailing_diamond": "", "type": "path"}], "type": "prompt"}], "final_space": true, "version": 3}