{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "segments": [{"background": "#feae34", "foreground": "#262b44", "leading_diamond": "", "powerline_symbol": "", "properties": {"style": "folder"}, "style": "diamond", "template": "  {{ .Path }} ", "trailing_diamond": "", "type": "path"}, {"background": "#fee761", "background_templates": ["{{ if or (.Working.Changed) (.Staging.Changed) }}#f77622{{ end }}", "{{ if and (gt .Ahead 0) (gt .Behind 0) }}#e43b44{{ end }}", "{{ if gt .Ahead 0 }}#2ce8f5{{ end }}", "{{ if gt .Behind 0 }}#f77622{{ end }}"], "foreground": "#262b44", "powerline_symbol": "", "properties": {"fetch_stash_count": true, "fetch_status": true, "fetch_upstream_icon": true}, "style": "powerline", "template": " {{ .UpstreamIcon }}{{ .HEAD }}{{if .BranchStatus }} {{ .BranchStatus }}{{ end }}{{ if .Staging.Changed }}  {{ .Staging.String }}{{ end }}{{ if and (.Working.Changed) (.Staging.Changed) }} |{{ end }}{{ if .Working.Changed }}  {{ .Working.String }}{{ end }}{{ if gt .StashCount 0 }}  {{ .StashCount }}{{ end }} ", "type": "git"}, {"background": "#0095e9", "background_templates": ["{{ if gt .Code 0 }}#ff0044{{ end }}"], "foreground": "#ffffff", "leading_diamond": "<transparent,background></>", "properties": {"always_enabled": true}, "style": "diamond", "template": "  ", "trailing_diamond": "", "type": "status"}], "type": "prompt"}], "final_space": true, "version": 3}