{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "newline": true, "segments": [{"background": "#fbfbfb", "foreground": "#0077c2", "leading_diamond": "", "properties": {"arch": "", "debian": "", "fedora": "", "linux": "", "macos": "", "manjaro": "", "opensuse": "", "ubuntu": "", "windows": ""}, "style": "diamond", "template": " {{ if .WSL }}WSL at {{ end }}{{.Icon}} ", "type": "os"}, {"background": "#fbfbfb", "foreground": "#0077c2", "powerline_symbol": "", "properties": {"display_host": true}, "style": "powerline", "template": "{{ .UserName }}<#000000>@</><#e06c75>{{ .HostName }}</> ", "type": "session"}, {"background": "#e06c75", "foreground": "#ffffff", "powerline_symbol": "", "style": "powerline", "template": " ⠀", "type": "root"}, {"background": "#0077c2", "foreground": "#ffffff", "powerline_symbol": "", "properties": {"folder_separator_icon": "/", "home_icon": "~", "max_depth": 2, "style": "letter"}, "style": "powerline", "template": "  {{ .Path }} ", "type": "path"}, {"background": "#fffb38", "background_templates": ["{{ if or (.Working.Changed) (.Staging.Changed) }}#ff9248{{ end }}", "{{ if and (gt .Ahead 0) (gt .Behind 0) }}#f26d50{{ end }}", "{{ if gt .Ahead 0 }}#f17c37{{ end }}", "{{ if gt .Behind 0 }}#89d1dc{{ end }}"], "foreground": "#193549", "powerline_symbol": "", "properties": {"fetch_stash_count": true, "fetch_status": true, "fetch_upstream_icon": true}, "style": "powerline", "template": " {{ .UpstreamIcon }}{{ .HEAD }}{{if .BranchStatus }} {{ .BranchStatus }}{{ end }}{{ if .Staging.Changed }}  {{ .Staging.String }}{{ end }}{{ if and (.Working.Changed) (.Staging.Changed) }} |{{ end }}{{ if .Working.Changed }}  {{ .Working.String }}{{ end }}{{ if gt .StashCount 0 }}  {{ .StashCount }}{{ end }} ", "type": "git"}, {"foreground": "#FFD54F", "style": "plain", "template": " {{if .Root}}#{{else}}${{end}}", "type": "text"}], "type": "prompt"}, {"segments": [{"foreground": "#ffffff", "properties": {"always_enabled": true}, "style": "plain", "template": " {{ if gt .Code 0 }}󰀰{{ else }}{{ end }} ", "type": "status"}, {"foreground": "#ffffff", "properties": {"always_enabled": true}, "style": "plain", "template": "{{ .FormattedMs }} ", "type": "executiontime"}, {"background": "#f36943", "background_templates": ["{{if eq \"Charging\" .State.String}}#40c4ff{{end}}", "{{if eq \"Discharging\" .State.String}}#ff5722{{end}}", "{{if eq \"Full\" .State.String}}#4caf50{{end}}"], "foreground": "#ffffff", "invert_powerline": true, "powerline_symbol": "", "properties": {"charged_icon": " ", "charging_icon": " ", "discharging_icon": " "}, "style": "powerline", "template": " {{ if not .Error }}{{ .Icon }}{{ .Percentage }}{{ end }}{{ .Error }}% ", "type": "battery"}, {"background": "#61afef", "foreground": "#ffffff", "invert_powerline": true, "properties": {"time_format": "15:04 (Mon)"}, "style": "diamond", "template": " {{ .CurrentDate | date .Format }} ", "trailing_diamond": "", "type": "time"}], "type": "rprompt"}], "console_title_template": "{{.UserName}}@{{.HostName}} : {{.Folder}}", "final_space": true, "version": 3}