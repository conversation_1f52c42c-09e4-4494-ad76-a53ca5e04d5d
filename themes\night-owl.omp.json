{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "segments": [{"background": "#21c7a8", "foreground": "#011627", "leading_diamond": "╭─", "properties": {"windows": ""}, "style": "diamond", "template": " {{ if .WSL }}WSL at {{ end }}{{.Icon}}  ", "trailing_diamond": "", "type": "os"}, {"background": "#ef5350", "foreground": "#ffeb95", "powerline_symbol": "", "style": "powerline", "template": "  ", "type": "root"}, {"background": "#82AAFF", "foreground": "#011627", "powerline_symbol": "", "leading_powerline_symbol": "", "properties": {"folder_icon": " ", "folder_separator_icon": "<#011627></> ", "home_icon": "  ", "style": "agnoster"}, "style": "powerline", "template": "{{ path .Path .Location }}", "type": "path"}, {"background": "#addb67", "background_templates": ["{{ if or (.Working.Changed) (.Staging.Changed) }}#e4cf6a{{ end }}", "{{ if and (gt .Ahead 0) (gt .Behind 0) }}#f78c6c{{ end }}", "{{ if gt .Ahead 0 }}#C792EA{{ end }}", "{{ if gt .Behind 0 }}#c792ea{{ end }}"], "foreground": "#011627", "powerline_symbol": "", "properties": {"branch_icon": " ", "fetch_stash_count": true, "fetch_status": true, "fetch_upstream_icon": true, "fetch_worktree_count": true, "mapped_branches": {"feat/*": "🚀 ", "bug/*": "🐛 "}}, "style": "powerline", "template": " {{ .UpstreamIcon }} {{ .HEAD }}{{if .BranchStatus }} {{ .BranchStatus }}{{ end }}{{ if .Working.Changed }}  {{ .Working.String }}{{ end }}{{ if and (.Working.Changed) (.Staging.Changed) }} |{{ end }}{{ if .Staging.Changed }}  {{ .Staging.String }}{{ end }}{{ if gt .StashCount 0 }}  {{ .StashCount }}{{ end }} ", "type": "git"}, {"background": "#575656", "foreground": "#d6deeb", "leading_diamond": "", "properties": {"style": "roundrock", "threshold": 0}, "style": "diamond", "template": " {{ .FormattedMs }}", "trailing_diamond": "", "type": "executiontime"}], "type": "prompt"}, {"alignment": "right", "overflow": "break", "segments": [{"background": "#d6deeb", "foreground": "#011627", "leading_diamond": "", "style": "diamond", "template": "  {{ .Name }} ", "trailing_diamond": "", "type": "shell"}, {"background": "#8f43f3", "foreground": "#ffffff", "leading_diamond": "", "style": "diamond", "template": " {{ round .PhysicalPercentUsed .Precision }}% ", "trailing_diamond": "", "type": "sysinfo"}, {"background": "#ffffff", "foreground": "#ce092f", "leading_diamond": "", "style": "diamond", "template": " {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }} ", "trailing_diamond": "", "type": "angular"}, {"background": "#ffffff", "foreground": "#de1f84", "leading_diamond": "", "style": "diamond", "template": "α {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }} ", "trailing_diamond": "", "type": "aurelia"}, {"background": "#565656", "foreground": "#faa029", "leading_diamond": "", "style": "diamond", "template": " {{ .Profile }}{{ if .Region }}@{{ .Region }}{{ end }} ", "trailing_diamond": "", "type": "aws"}, {"background": "#ffffff", "foreground": "#337bb6", "leading_diamond": "", "style": "diamond", "template": " {{ .<PERSON><PERSON> }} ", "trailing_diamond": "", "type": "az"}, {"background": "#ffffff", "foreground": "#5398c2", "leading_diamond": "", "style": "diamond", "template": "<#f5bf45></> {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }} ", "trailing_diamond": "", "type": "azfunc"}, {"background": "#5a7a94", "foreground": "#100e23", "leading_diamond": "", "style": "diamond", "template": "  cds {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }} ", "trailing_diamond": "", "type": "cds"}, {"background": "#ffffff", "foreground": "#000000", "leading_diamond": "", "style": "diamond", "template": " {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }} ", "trailing_diamond": "", "type": "crystal"}, {"background": "#ffffff", "foreground": "#1d5185", "leading_diamond": "", "style": "diamond", "template": "  cf {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }} ", "trailing_diamond": "", "type": "cf"}, {"background": "#ffffff", "foreground": "#1d5185", "leading_diamond": "", "style": "diamond", "template": " {{if .Org }}{{ .Org }}{{ end }}{{ if .Space }}/{{ .Space }}{{ end }} ", "trailing_diamond": "", "type": "cftarget"}, {"background": "#d2d2d2", "foreground": "#01a300", "leading_diamond": "", "style": "diamond", "template": "<#2829b2></> <#be1818></>  cmake {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }} ", "trailing_diamond": "", "type": "cmake"}, {"background": "#e1e8e9", "foreground": "#055b9c", "leading_diamond": "", "style": "diamond", "template": " {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }} ", "trailing_diamond": "", "type": "dart"}, {"background": "#0e0e0e", "foreground": "#0d6da8", "leading_diamond": "", "style": "diamond", "template": "  {{ if .Unsupported }}{{ else }}{{ .Full }}{{ end }} ", "trailing_diamond": "", "type": "dotnet"}, {"background": "#06A4CE", "foreground": "#ffffff", "leading_diamond": "", "style": "diamond", "template": " {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }} ", "trailing_diamond": "", "type": "flutter"}, {"background": "#ffffff", "foreground": "#06aad5", "leading_diamond": "", "style": "diamond", "template": " {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }} ", "trailing_diamond": "", "type": "go"}, {"background": "#100e23", "foreground": "#906cff", "leading_diamond": "", "style": "diamond", "template": " {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }} ", "trailing_diamond": "", "type": "haskell"}, {"background": "#ffffff", "foreground": "#ec2729", "leading_diamond": "", "style": "diamond", "template": " {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }} ", "trailing_diamond": "", "type": "java"}, {"background": "#945bb3", "foreground": "#ffffff", "leading_diamond": "", "style": "diamond", "template": "<#ca3c34></> {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }} ", "trailing_diamond": "", "type": "julia"}, {"background": "#906cff", "foreground": "#ffffff", "leading_diamond": "", "style": "diamond", "template": "K {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }} ", "trailing_diamond": "", "type": "kotlin"}, {"background": "#316ce4", "foreground": "#ffffff", "leading_diamond": "", "style": "diamond", "template": " {{.Context}} :: {{if .Namespace}}{{.Namespace}}{{else}}default{{end}} ", "trailing_diamond": "", "type": "kubectl"}, {"background": "#ffffff", "foreground": "#000081", "leading_diamond": "", "style": "diamond", "template": " {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }} ", "trailing_diamond": "", "type": "lua"}, {"background": "#303030", "foreground": "#3C873A", "leading_diamond": "", "properties": {"fetch_package_manager": true, "npm_icon": "<#cc3a3a></> ", "yarn_icon": "<#348cba></> "}, "style": "diamond", "template": " {{ if .PackageManagerIcon }}{{ .PackageManagerIcon }} {{ end }}{{ .Full }} ", "trailing_diamond": "", "type": "node"}, {"background": "#6488c0", "foreground": "#1e293b", "leading_diamond": "", "style": "diamond", "template": "Nx {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }} ", "trailing_diamond": "", "type": "nx"}, {"background": "#41436d", "foreground": "#ffffff", "leading_diamond": "", "style": "diamond", "template": " {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }} ", "trailing_diamond": "", "type": "perl"}, {"background": "#787CB5", "foreground": "#000000", "leading_diamond": "", "style": "diamond", "template": " {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }} ", "trailing_diamond": "", "type": "php"}, {"background": "#306998", "foreground": "#FFE873", "leading_diamond": "", "style": "diamond", "template": "  {{ if .Error }}{{ .Error }}{{ else }}{{ if .Venv }}{{ .Venv }} {{ end }}{{ .Full }}{{ end }}", "trailing_diamond": "", "type": "python"}, {"background": "#b9bbbf", "foreground": "#1c68bd", "leading_diamond": "", "style": "diamond", "template": "R {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }} ", "trailing_diamond": "", "type": "r"}, {"background": "#ffffff", "foreground": "#9c1006", "leading_diamond": "", "style": "diamond", "template": " {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }} ", "trailing_diamond": "", "type": "ruby"}, {"background": "#ffffff", "foreground": "#000000", "leading_diamond": "", "style": "diamond", "template": " {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }} ", "trailing_diamond": "", "type": "rust"}, {"background": "#fe562e", "foreground": "#ffffff", "leading_diamond": "", "style": "diamond", "template": " {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }} ", "trailing_diamond": "", "type": "swift"}, {"background": "#234d70", "foreground": "#d6deeb", "leading_diamond": "", "properties": {"time_format": "15:04:05"}, "style": "diamond", "template": " {{ .CurrentDate | date .Format }}", "trailing_diamond": "", "type": "time"}], "type": "prompt"}, {"alignment": "left", "newline": true, "segments": [{"foreground": "#21c7a8", "style": "plain", "template": "╰─", "type": "text"}, {"background": "#1DB954", "foreground": "#011627", "leading_diamond": "", "properties": {"playing_icon": " "}, "style": "diamond", "template": " {{ .Icon }}{{ if ne .Status \"stopped\" }}{{ .Artist }} ~ {{ .Track }}{{ end }} ", "trailing_diamond": " ", "type": "spotify"}, {"foreground": "#22da6e", "foreground_templates": ["{{ if gt .Code 0 }}#ef5350{{ end }}"], "properties": {"always_enabled": true}, "style": "plain", "template": "", "type": "status"}], "type": "prompt"}], "console_title_template": "{{ .Folder }}", "final_space": true, "transient_prompt": {"background": "transparent", "foreground": "#d6deeb", "template": " "}, "version": 3}