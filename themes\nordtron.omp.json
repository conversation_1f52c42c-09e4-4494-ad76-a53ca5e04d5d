{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "segments": [{"foreground": "#88c0d0", "style": "plain", "template": "<#5e81ac>┏[</>{{ .UserName }}<#5e81ac>]</>", "type": "session"}, {"foreground": "#b48ead", "properties": {"fetch_stash_count": true, "fetch_status": true, "fetch_upstream_icon": true}, "style": "plain", "template": "<#5e81ac>--[</>{{ .HEAD }}{{if .BranchStatus }} {{ .BranchStatus }}{{ end }}{{ if .Working.Changed }}<#8fbcbb> ● </>{{ end }}{{ if .Staging.Changed }}<#88c0d0> ● </>{{ end }}<#5e81ac>]</>", "type": "git"}, {"foreground": "#b48ead", "style": "plain", "template": "<#5e81ac>--[</>{{.Profile}}{{if .Region}}@{{.Region}}{{end}}<#5e81ac>]</>", "type": "aws"}, {"foreground": "#b48ead", "style": "plain", "template": "<#5e81ac>--[</>{{.Context}}{{if .Namespace}} :: {{.Namespace}}{{end}}<#5e81ac>]</>", "type": "kubectl"}, {"foreground": "#d8dee9", "style": "plain", "template": "<#5e81ac>[</><#5e81ac>]</>", "type": "root"}, {"foreground": "#d8dee9", "style": "plain", "template": "<#5e81ac>[x</>{{ reason .Code }}<#5e81ac>]</>", "type": "status"}], "type": "prompt"}, {"alignment": "left", "newline": true, "segments": [{"foreground": "#88c0d0", "properties": {"style": "full"}, "style": "plain", "template": "<#5e81ac>┖[</>{{ .Path }}<#5e81ac>]</>", "type": "path"}], "type": "prompt"}, {"alignment": "left", "newline": true, "segments": [{"foreground": "#5e81ac", "style": "plain", "template": "  ", "type": "text"}], "type": "prompt"}], "final_space": true, "version": 3}