{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "newline": true, "segments": [{"foreground": "red", "powerline_symbol": "", "style": "plain", "template": " ", "type": "root"}, {"foreground": "blue", "properties": {"folder_icon": "", "folder_separator_icon": "/", "home_icon": "", "style": "agnoster"}, "style": "plain", "template": "{{ .Path }}", "type": "path"}, {"foreground": "green", "foreground_templates": ["{{ if or (.Working.Changed) (.Staging.Changed) }}yellow{{ end }}", "{{ if and (gt .Ahead 0) (gt .Behind 0) }}red{{ end }}", "{{ if gt .Ahead 0 }}red{{ end }}", "{{ if gt .Behind 0 }}green{{ end }}"], "properties": {"fetch_stash_count": true, "fetch_status": true, "fetch_upstream_icon": true, "github_icon": " "}, "style": "plain", "template": " on {{ .UpstreamIcon }}{{ .HEAD }}{{if .BranchStatus }} {{ .BranchStatus }}{{ end }}{{ if .Working.Changed }}<red>  {{ .Working.String }}</>{{ end }}{{ if and (.Working.Changed) (.Staging.Changed) }} |{{ end }}{{ if .Staging.Changed }}<yellow>  {{ .Staging.String }}</>{{ end }}{{ if gt .StashCount 0 }}  {{ .StashCount }}{{ end }}", "type": "git"}, {"foreground": "magenta", "style": "plain", "template": " [.NET] {{ if .Unsupported }}{{ else }}{{ .Full }}{{ end }} ", "type": "dotnet"}], "type": "prompt"}, {"alignment": "left", "newline": true, "segments": [{"foreground": "yellow", "properties": {"time_format": "15:04:05"}, "style": "plain", "template": "{{ .CurrentDate | date .Format }} ", "type": "time"}, {"foreground": "green", "style": "plain", "template": "❯", "type": "text"}], "type": "prompt"}], "final_space": true, "version": 3}