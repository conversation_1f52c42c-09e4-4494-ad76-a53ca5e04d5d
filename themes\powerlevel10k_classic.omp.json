{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "segments": [{"background": "#546E7A", "foreground": "#26C6DA", "style": "plain", "template": " {{ if .WSL }}WSL at {{ end }}{{.Icon}} ", "type": "os"}, {"background": "#546E7A", "foreground": "#26C6DA", "style": "plain", "template": "  ", "type": "root"}, {"background": "#546E7A", "foreground": "#26C6DA", "properties": {"style": "full"}, "style": "plain", "template": " {{ .Path }} ", "type": "path"}, {"background": "#546E7A", "foreground": "#D4E157", "style": "plain", "template": "<#26C6DA> </>{{ .HEAD }} ", "type": "git"}, {"background": "transparent", "foreground": "#546E7A", "style": "plain", "template": "", "type": "text"}], "type": "prompt"}, {"alignment": "right", "segments": [{"background": "#546E7A", "foreground": "#D4E157", "leading_diamond": "", "style": "diamond", "template": " {{ .UserName }}@{{ .HostName }} <#26C6DA></> ", "type": "session"}, {"background": "#546E7A", "foreground": "#D4E157", "properties": {"time_format": "15:04:05"}, "style": "plain", "template": " {{ .CurrentDate | date .Format }}  ", "type": "time"}], "type": "prompt"}, {"alignment": "left", "newline": true, "segments": [{"foreground": "#D4E157", "foreground_templates": ["{{ if gt .Code 0 }}#FF5252{{ end }}"], "properties": {"always_enabled": true}, "style": "plain", "template": "❯ ", "type": "status"}], "type": "prompt"}], "version": 3}