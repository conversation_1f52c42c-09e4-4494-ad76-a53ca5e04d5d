{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "segments": [{"background": "#d3d7cf", "foreground": "#000000", "leading_diamond": "╭─", "style": "diamond", "template": " {{ if .WSL }}WSL at {{ end }}{{.Icon}} ", "type": "os"}, {"background": "#3465a4", "foreground": "#e4e4e4", "powerline_symbol": "", "properties": {"home_icon": "~", "style": "full"}, "style": "powerline", "template": "  {{ .Path }} ", "type": "path"}, {"background": "#4e9a06", "background_templates": ["{{ if or (.Working.Changed) (.Staging.Changed) }}#c4a000{{ end }}", "{{ if and (gt .Ahead 0) (gt .Behind 0) }}#f26d50{{ end }}", "{{ if gt .Ahead 0 }}#89d1dc{{ end }}", "{{ if gt .Behind 0 }}#4e9a06{{ end }}"], "foreground": "#000000", "powerline_symbol": "", "properties": {"branch_icon": " ", "fetch_stash_count": true, "fetch_status": true, "fetch_upstream_icon": true}, "style": "powerline", "template": " {{ .UpstreamIcon }}{{ .HEAD }}{{if .BranchStatus }} {{ .BranchStatus }}{{ end }}{{ if .Working.Changed }}  {{ .Working.String }}{{ end }}{{ if and (.Working.Changed) (.Staging.Changed) }} |{{ end }}{{ if .Staging.Changed }}  {{ .Staging.String }}{{ end }}{{ if gt .StashCount 0 }}  {{ .StashCount }}{{ end }} ", "type": "git"}], "type": "prompt"}, {"alignment": "right", "segments": [{"background": "#689f63", "foreground": "#ffffff", "invert_powerline": true, "powerline_symbol": "", "properties": {"fetch_version": true}, "style": "powerline", "template": " {{ if .PackageManagerIcon }}{{ .PackageManagerIcon }} {{ end }}{{ .Full }}  ", "type": "node"}, {"background": "#00acd7", "foreground": "#111111", "invert_powerline": true, "powerline_symbol": "", "properties": {"fetch_version": true}, "style": "powerline", "template": " {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }}  ", "type": "go"}, {"background": "#4063D8", "foreground": "#111111", "invert_powerline": true, "powerline_symbol": "", "properties": {"fetch_version": true}, "style": "powerline", "template": " {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }}  ", "type": "julia"}, {"background": "#FFDE57", "foreground": "#111111", "invert_powerline": true, "powerline_symbol": "", "properties": {"display_mode": "files", "fetch_virtual_env": false}, "style": "powerline", "template": " {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }}  ", "type": "python"}, {"background": "#AE1401", "foreground": "#ffffff", "invert_powerline": true, "powerline_symbol": "", "properties": {"display_mode": "files", "fetch_version": true}, "style": "powerline", "template": " {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }}  ", "type": "ruby"}, {"background": "#FEAC19", "foreground": "#ffffff", "invert_powerline": true, "powerline_symbol": "", "properties": {"display_mode": "files", "fetch_version": false}, "style": "powerline", "template": " {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }} ", "type": "azfunc"}, {"background_templates": ["{{if contains \"default\" .Profile}}#FFA400{{end}}", "{{if contains \"jan\" .<PERSON>}}#f1184c{{end}}"], "foreground": "#ffffff", "invert_powerline": true, "powerline_symbol": "", "properties": {"display_default": false}, "style": "powerline", "template": " {{ .Profile }}{{ if .Region }}@{{ .Region }}{{ end }}  ", "type": "aws"}, {"background": "#ffff66", "foreground": "#111111", "invert_powerline": true, "powerline_symbol": "", "style": "powerline", "template": "  ", "type": "root"}, {"background": "#c4a000", "foreground": "#000000", "invert_powerline": true, "powerline_symbol": "", "style": "powerline", "template": " {{ .FormattedMs }}  ", "type": "executiontime"}, {"background": "#000000", "background_templates": ["{{ if gt .Code 0 }}#cc2222{{ end }}"], "foreground": "#d3d7cf", "invert_powerline": true, "powerline_symbol": "", "properties": {"always_enabled": true}, "style": "powerline", "template": " {{ if gt .Code 0 }}{{ reason .Code }}{{ else }}{{ end }} ", "type": "status"}, {"background": "#d3d7cf", "foreground": "#000000", "invert_powerline": true, "style": "diamond", "template": " {{ .CurrentDate | date .Format }}  ", "trailing_diamond": "─╮", "type": "time"}], "type": "prompt"}, {"alignment": "left", "newline": true, "segments": [{"foreground": "#d3d7cf", "style": "plain", "template": "╰─", "type": "text"}], "type": "prompt"}, {"segments": [{"foreground": "#d3d7cf", "style": "plain", "template": "─╯", "type": "text"}], "type": "rprompt"}], "console_title_template": "{{ .Shell }} in {{ .Folder }}", "final_space": true, "version": 3}