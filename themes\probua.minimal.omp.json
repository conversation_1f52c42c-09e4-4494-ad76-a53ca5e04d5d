{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "segments": [{"foreground": "#ffdd86", "style": "plain", "template": "{{ .UserName }}@{{ .HostName }} ", "type": "session"}, {"foreground": "#42a9ff", "style": "plain", "properties": {"style": "full"}, "template": "{{ .Path }} ", "type": "path"}, {"properties": {"branch_icon": "", "fetch_status": true}, "style": "plain", "template": "git:{{ if or (.Working.Changed) (.Staging.Changed) (gt .StashCount 0) }}<#ffdd86>{{ .HEAD }}</>{{ else }}{{ .HEAD }}{{ end }}{{ if .Staging.Changed }} <#98c379>{{ .Staging.String }}</>{{ end }}{{ if .Working.Changed }} <#d16971>{{ .Working.String }}</>{{ end }}", "type": "git"}], "type": "prompt"}, {"alignment": "left", "newline": true, "segments": [{"foreground": "#ffdd86", "foreground_templates": ["{{ if gt .Code 0 }}#42a9ff{{ end }}"], "properties": {"always_enabled": true}, "style": "plain", "template": "> ", "type": "status"}], "type": "prompt"}], "version": 3}