{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "segments": [{"background": "#BF231D", "foreground": "#ffffff", "style": "plain", "template": "  ", "type": "root"}, {"background": "#0A703E", "foreground": "#ffffff", "style": "plain", "template": " {{ .<PERSON><PERSON> }} ", "type": "os"}, {"background": "#0A703E", "foreground": "#ffffff", "style": "plain", "template": "{{ .<PERSON>r<PERSON><PERSON> }} ", "type": "session"}, {"background": "#256C9D", "foreground": "#ffffff", "properties": {"folder_icon": "", "folder_separator_icon": "  ", "max_depth": 2, "style": "agnoster_short"}, "style": "plain", "template": " {{ .Path }} ", "type": "path"}, {"background": "#256C9D", "foreground": "#ffffff", "properties": {"branch_template": "{{ trunc 30 .Branch }}", "fetch_stash_count": false, "fetch_status": true, "fetch_upstream_icon": true}, "style": "plain", "template": "[ {{ .UpstreamIcon }}{{ .HEAD }}{{if .BranchStatus }} {{ .BranchStatus }}{{ end }}{{ if .Working.Changed }}  {{ .Working.String }}{{ end }}{{ if and (.Working.Changed) (.Staging.Changed) }} |{{ end }}{{ if .Staging.Changed }}  {{ .Staging.String }}{{ end }}{{ if gt .StashCount 0 }}  {{ .StashCount }}{{ end }} ]", "type": "git"}, {"background": "#256C9D", "foreground": "#ffffff", "powerline_symbol": "", "style": "plain", "template": "  {{ if .Error }}{{ .Error }}{{ else }}{{ if .Venv }}{{ .Venv }} {{ end }}{{ .Full }}{{ end }} ", "properties": {"text": ""}, "type": "python"}, {"foreground": "#256C9D", "style": "plain", "template": " ", "type": "text"}], "type": "prompt"}], "version": 3}