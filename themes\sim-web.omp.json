{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"type": "prompt", "alignment": "right", "overflow": "hide", "segments": [{"type": "executiontime", "style": "powerline", "foreground": "#a9ffb4", "template": " {{ .FormattedMs }}s <#ffffff></>", "properties": {"threshold": 0, "style": "dallas"}}, {"type": "node", "style": "powerline", "foreground": "#45bf17", "template": "  {{ .Full }} "}, {"type": "npm", "style": "powerline", "foreground": "#FE4A49", "template": "<#F3EFF5>and</>  {{ .Full }} "}]}, {"type": "prompt", "alignment": "left", "newline": true, "overflow": "break", "segments": [{"type": "path", "style": "powerline", "foreground": "#ffafd2", "properties": {"style": "agnoster_full", "home_icon": "home", "folder_icon": "", "folder_separator_icon": " ❯ "}, "template": " {{ .Path }} "}, {"type": "git", "style": "powerline", "foreground": "#f14e32", "properties": {"branch_icon": " "}, "template": "({{ .HEAD }})"}]}, {"alignment": "left", "newline": true, "type": "prompt", "segments": [{"type": "status", "style": "diamond", "foreground": "#00c7fc", "properties": {"always_enabled": true}, "template": "<#00c7fc>❯</>_: "}]}], "console_title_template": "{{ .Folder }}", "transient_prompt": {"background": "transparent", "foreground": "#FEF5ED", "template": "{{ .Shell }}"}, "version": 3}