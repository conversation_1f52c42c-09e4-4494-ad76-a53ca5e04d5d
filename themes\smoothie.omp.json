{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "newline": true, "segments": [{"foreground": "#b1ff4f", "style": "powerline", "template": "[🐍 {{ if .Error }}{{ .Error }}{{ else }}{{ if .Venv }}{{ .Venv }} {{ end }}{{ .Full }}{{ end }}] ", "type": "python"}, {"foreground": "#ffaed8", "properties": {"folder_separator_icon": " | ", "home_icon": "", "style": "letter"}, "style": "plain", "template": "<#eaeaea>⎧</> ⟨{{ .Path }}⟩ ", "type": "path"}, {"foreground": "#62beff", "properties": {"branch_icon": "", "branch_identical_icon": "≡", "fetch_status": true}, "style": "plain", "template": "⟨{{ .HEAD }}{{if .BranchStatus }} {{ .BranchStatus }}{{ end }}{{ if .Working.Changed }} ∆{{ .Working.String }}{{ end }}{{ if and (.Working.Changed) (.Staging.Changed) }} |{{ end }}{{ if .Staging.Changed }}  {{ .Staging.String }}{{ end }}⟩", "type": "git"}, {"foreground": "#98C379", "properties": {"fetch_version": true}, "style": "plain", "template": "<#ffffff>◦</> ☢{{ if .PackageManagerIcon }}{{ .PackageManagerIcon }} {{ end }}{{ .Full }} ", "type": "node"}], "type": "prompt"}, {"alignment": "right", "segments": [{"foreground": "#fb0207", "style": "plain", "template": " ××× ", "type": "status"}, {"foreground": "#9966ff", "properties": {"style": "austin", "threshold": 0}, "style": "plain", "template": "⟨{{ .FormattedMs }}⟩ <#eaeaea>⎫</>", "type": "executiontime"}], "type": "prompt"}, {"alignment": "left", "newline": true, "segments": [{"foreground": "#3ce6bf", "style": "plain", "template": "<#eaeaea>⎩</>⟩⟩⟩⟩⟩⟩ ", "type": "text"}], "type": "prompt"}, {"alignment": "right", "segments": [{"foreground": "#f5f5a6", "properties": {"time_format": "15:04:05"}, "style": "plain", "template": "⟨{{ .CurrentDate | date .Format }}⟩ <#eaeaea>⎭</>", "type": "time"}], "type": "rprompt"}], "version": 3}