{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "segments": [{"foreground": "#26C6DA", "properties": {"macos": "mac"}, "style": "plain", "template": "{{ if .WSL }}WSL at {{ end }}{{.Icon}}", "type": "os"}, {"foreground": "#26C6DA", "style": "plain", "template": " {{ .<PERSON>r<PERSON><PERSON> }}: ", "type": "session"}, {"foreground": "lightGreen", "properties": {"style": "folder"}, "style": "plain", "template": "{{ .Path }} ", "type": "path"}, {"properties": {"branch_icon": "", "fetch_stash_count": true}, "style": "plain", "template": "<#ffffff>on</> {{ .HEAD }}{{ if gt .StashCount 0 }}  {{ .StashCount }}{{ end }} ", "type": "git"}, {"foreground": "#906cff", "style": "powerline", "template": "[ {{ if .Error }}{{ .Error }}{{ else }}{{ if .Venv }}{{ .Venv }} {{ end }}{{ .Full }}{{ end }}] ", "type": "python"}, {"foreground": "#7FD5EA", "style": "powerline", "template": "[ {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }}] ", "type": "go"}, {"foreground": "#76b367", "style": "powerline", "template": "[ {{ if .PackageManagerIcon }}{{ .PackageManagerIcon }} {{ end }}{{ .Full }}] ", "type": "node"}, {"foreground": "#f44336", "style": "powerline", "template": "[{{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }}] ", "type": "ruby"}, {"foreground": "#ea2d2e", "style": "powerline", "template": "[ {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }}] ", "type": "java"}, {"foreground": "#4063D8", "style": "powerline", "template": "  {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }} ", "type": "julia"}, {"foreground": "#FFD54F", "style": "plain", "template": "❯ ", "type": "text"}], "type": "prompt"}], "version": 3}