{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "segments": [{"foreground": "#E06C75", "style": "plain", "template": "root <#ffffff>in</> ", "type": "root"}, {"foreground": "#55B9C4", "properties": {"style": "folder"}, "style": "plain", "template": "{{ .Path }} ", "type": "path"}, {"foreground": "#C678DD", "properties": {"fetch_status": true}, "style": "plain", "template": "<#ffffff>on</> {{ .HEAD }}{{if .BranchStatus }} {{ .BranchStatus }}{{ end }}{{ if .Working.Changed }}  {{ .Working.String }}{{ end }}{{ if and (.Working.Changed) (.Staging.Changed) }} |{{ end }}{{ if .Staging.Changed }}  {{ .Staging.String }}{{ end }} ", "type": "git"}, {"foreground": "#98C379", "properties": {"fetch_version": true}, "style": "plain", "template": "<#ffffff>via</>  {{ if .PackageManagerIcon }}{{ .PackageManagerIcon }} {{ end }}{{ .Full }} ", "type": "node"}, {"foreground": "#C94A16", "style": "plain", "template": "x ", "type": "status"}], "type": "prompt"}, {"alignment": "left", "newline": true, "segments": [{"foreground": "#63F08C", "style": "plain", "template": "➜ ", "type": "text"}], "type": "prompt"}], "version": 3}