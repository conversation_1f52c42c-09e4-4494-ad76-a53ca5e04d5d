{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "segments": [{"foreground": "#15C2CB", "properties": {"style": "folder"}, "style": "plain", "template": "{{ .Path }} ", "type": "path"}, {"foreground": "#F141A8", "properties": {"branch_icon": " "}, "style": "plain", "template": "<#F3EFF5>on</> {{ .HEAD }}{{if .BranchStatus }} {{ .BranchStatus }}{{ end }}{{ if .Working.Changed }}  {{ .Working.String }}{{ end }}{{ if and (.Working.Changed) (.Staging.Changed) }} |{{ end }}{{ if .Staging.Changed }}  {{ .Staging.String }}{{ end }} ", "type": "git"}, {"foreground": "#5EADF2", "style": "plain", "template": "{{if .Version}}<#F3EFF5>is</>  {{.Version}} {{end}}", "type": "project"}, {"foreground": "#44FFD2", "properties": {"fetch_version": true}, "style": "plain", "template": "<#F3EFF5>via</>  {{ .Full }} ", "type": "node"}, {"foreground": "#FE4A49", "style": "plain", "template": "<#F3EFF5>and</>  {{.Full}} ", "type": "npm"}], "type": "prompt"}, {"alignment": "left", "newline": true, "segments": [{"foreground": "#FFE45E", "style": "plain", "template": "❯ ", "type": "text"}], "type": "prompt"}], "version": 3}