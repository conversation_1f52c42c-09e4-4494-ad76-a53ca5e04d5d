{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "segments": [{"background": "#2e9599", "foreground": "#ffffff", "leading_diamond": "", "powerline_symbol": "", "style": "diamond", "template": " {{ .<PERSON><PERSON><PERSON> }} ", "type": "session"}, {"background": "#0080ff", "foreground": "#ffffff", "powerline_symbol": "", "properties": {"style": "full"}, "style": "powerline", "template": "  {{ .Path }} ", "type": "path"}, {"background": "#c19c00", "background_templates": ["{{ if or (.Working.Changed) (.Staging.Changed) }}#FFEB3B{{ end }}", "{{ if and (gt .Ahead 0) (gt .Behind 0) }}#FFA300{{ end }}", "{{ if gt .Ahead 0 }}#FF7070{{ end }}", "{{ if gt .Behind 0 }}#90F090{{ end }}"], "foreground": "#000000", "powerline_symbol": "", "properties": {"fetch_stash_count": true, "fetch_status": true, "fetch_upstream_icon": true}, "style": "powerline", "template": " {{ .UpstreamIcon }}{{ .HEAD }}{{if .BranchStatus }} {{ .BranchStatus }}{{ end }}{{ if .Working.Changed }}  {{ .Working.String }}{{ end }}{{ if and (.Working.Changed) (.Staging.Changed) }} |{{ end }}{{ if .Staging.Changed }}  {{ .Staging.String }}{{ end }}{{ if gt .StashCount 0 }}  {{ .StashCount }}{{ end }} ", "type": "git"}, {"background": "#2e9599", "background_templates": ["{{ if gt .Code 0 }}#f1184c{{ end }}"], "foreground": "#ffffff", "properties": {"always_enabled": true}, "style": "diamond", "template": "  ", "trailing_diamond": "", "type": "status"}], "type": "prompt"}, {"alignment": "right", "segments": [{"background": "#2e9599", "foreground": "#ffffff", "leading_diamond": "", "properties": {"time_format": "15:04:05"}, "style": "diamond", "template": " {{ .CurrentDate | date .Format }} ", "trailing_diamond": "", "type": "time"}], "type": "prompt"}, {"alignment": "left", "newline": true, "segments": [{"foreground": "#ff0000", "style": "plain", "template": " ", "type": "root"}, {"foreground": "#ffffff", "style": "plain", "template": " {{ .Name }} ", "type": "shell"}, {"foreground": "#ffffff", "style": "plain", "template": " ", "type": "text"}], "type": "prompt"}], "console_title_template": "{{ .Folder }}", "version": 3}