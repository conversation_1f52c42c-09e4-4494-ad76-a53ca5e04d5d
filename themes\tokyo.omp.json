{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "leading_diamond": "<#7eb8da>┏</>", "segments": [{"foreground": "#7eb8da", "properties": {"time_format": "01/02/2006 @ 3:04:05 PM MST"}, "style": "diamond", "template": "[<#ffffff></> {{ .CurrentDate | date .Format }}]", "type": "time"}, {"foreground": "#7eb8da", "style": "plain", "template": "[<#ffffff></> {{ .UserName }} :: <#ffffff></> {{ .HostName }}]", "type": "session"}, {"foreground": "#be9ddf", "style": "diamond", "template": "[<#ffffff></> RAM: {{ (div ((sub .PhysicalTotalMemory .PhysicalAvailableMemory)|float64) 1073741824.0) }}/{{ (div .PhysicalTotalMemory 1073741824.0) }}GB]", "type": "sysinfo"}, {"foreground": "#f36943", "foreground_templates": ["{{if eq \"Charging\" .State.String}}#40c4ff{{end}}", "{{if eq \"Discharging\" .State.String}}#ff5722{{end}}", "{{if eq \"Full\" .State.String}}#4caf50{{end}}"], "properties": {"charged_icon": "<#ffffff></> ", "charging_icon": " ", "discharging_icon": "<#ffff00></> "}, "style": "plain", "template": "[{{ if not .Error }}{{ .Icon }}{{ .Percentage }}{{ end }}{{ .Error }}]", "type": "battery"}, {"foreground": "#be9ddf", "properties": {"style": "dallas", "threshold": 0}, "style": "diamond", "template": "[<#ffffff></> {{ .FormattedMs }}s]", "type": "executiontime"}], "type": "prompt"}, {"alignment": "left", "newline": true, "leading_diamond": "<#7eb8da>┣</>", "segments": [{"foreground": "#7eb8da", "style": "diamond", "template": "[<#ffffff></> Subscription: <#ffff00>{{ .Name }}</>]", "type": "az"}, {"foreground": "#7eb8da", "style": "diamond", "template": "[<#ffffff></> Azure Developer CLI Environment: <#ffff00>{{ .DefaultEnvironment  }}</> :: <#ffff00>{{ .Version }}</>]", "type": "azd"}, {"foreground": "#7eb8da", "style": "diamond", "template": "[<#ffffff></> <#ffff00>{{ .Profile }}</>{{if .Region}}@<#ffff00>{{ .Region }}</>{{ end }}]", "type": "aws"}, {"foreground": "#7eb8da", "style": "diamond", "template": "[{{ if .Error }}{{ .Error }}{{ else }}<#ffffff></> <#ffff00>{{ .Project }}</> :: <#ffff00>{{.Account}}</>{{ end }}]", "type": "gcp"}, {"foreground": "#ffa5d8", "style": "diamond", "template": "[<#ffffff>e</> <#ffff00>{{.Context}}</>{{if .Namespace}} :: <#ffff00>{{.Namespace}}</>{{end}}]", "type": "kubectl"}, {"foreground": "#ffa5d8", "style": "diamond", "template": "[{{ if .Error }}{{ .Error }}{{ else }}{{ if .Name }}<#ffffff></> {{ .Name }}{{ end }}{{ if .Target }} {{ .Target }}{{ end }}{{ end }}]", "type": "project"}], "type": "prompt"}, {"alignment": "left", "newline": true, "leading_diamond": "<#7eb8da>┣</>", "segments": [{"foreground": "#7eb8da", "properties": {"style": "full"}, "style": "diamond", "template": "[<#ffffff></> <#98bfad>{{ .Path }}</>]", "type": "path"}, {"foreground": "#ffa5d8", "properties": {"fetch_stash_count": true, "fetch_status": true, "fetch_upstream_icon": true}, "style": "plain", "template": "[<#ffffff>{{ .UpstreamIcon }} </>{{ .HEAD }}{{if .BranchStatus }} {{ .BranchStatus }}{{ end }}{{ if .Working.Changed }} <#ffffff></> {{ .Working.String }}{{ end }}{{ if and (.Working.Changed) (.Staging.Changed) }} |{{ end }}{{ if .Staging.Changed }} <#ffffff></> {{ .Staging.String }}{{ end }}{{ if gt .StashCount 0 }} <#ffffff></> {{ .StashCount }}{{ end }}]", "type": "git"}], "type": "prompt"}, {"alignment": "left", "newline": true, "leading_diamond": "<#7eb8da>└─</>", "segments": [{"style": "diamond", "template": "<#ffff00>[#]</>", "type": "root"}, {"foreground": "#ffa5d8", "style": "diamond", "template": "[<#ffffff></> Error, check your command]", "type": "status"}, {"style": "diamond", "template": ">", "type": "text"}], "type": "prompt"}], "final_space": true, "secondary_prompt": {"background": "transparent", "template": " "}, "valid_line": {"background": "transparent", "template": "> "}, "error_line": {"background": "transparent", "template": "<#ff0000></> "}, "version": 3}