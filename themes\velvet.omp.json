{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "segments": [{"background": "#0E050F", "foreground": "#EFDCF9", "properties": {"arch": "", "debian": "", "fedora": "", "linux": "", "macos": "", "manjaro": "", "opensuse": "", "ubuntu": "", "windows": ""}, "style": "diamond", "template": " {{ if .WSL }}WSL at {{ end }}{{.Icon}} ", "type": "os"}, {"background": "#170B3B", "foreground": "#EFDCF9", "powerline_symbol": "", "properties": {"folder_icon": "...", "folder_separator_icon": "/", "home_icon": "~", "max_depth": 3, "style": "agnoster_short"}, "style": "powerline", "template": " {{ .Path }} ", "type": "path"}, {"background": "#341948", "foreground": "#EFDCF9", "powerline_symbol": "", "properties": {"branch_template": "{{ trunc 25 .<PERSON> }}", "fetch_stash_count": true, "fetch_status": true, "fetch_upstream_icon": true}, "style": "powerline", "template": " {{ .UpstreamIcon }}{{ .HEAD }}{{if .BranchStatus }} {{ .BranchStatus }}{{ end }}{{ if .Working.Changed }}  {{ .Working.String }}{{ end }}{{ if and (.Working.Changed) (.Staging.Changed) }} |{{ end }}{{ if .Staging.Changed }}  {{ .Staging.String }}{{ end }}{{ if gt .StashCount 0 }}  {{ .StashCount }}{{ end }} ", "type": "git"}, {"background": "#4c1f5e", "foreground": "#EFDCF9", "powerline_symbol": "", "properties": {"always_enabled": true}, "style": "powerline", "template": " {{ .FormattedMs }} ", "type": "executiontime"}, {"background": "#69307A", "foreground": "#EFDCF9", "foreground_templates": ["{{ if gt .Code 0 }}#FF3C3C{{ end }}"], "properties": {"always_enabled": true}, "style": "diamond", "template": " {{ if gt .Code 0 }} {{.Code}}{{ end }} ", "trailing_diamond": "", "type": "status"}], "type": "prompt"}, {"alignment": "right", "segments": [{"background": "#4c1f5e", "foreground": "#E4F34A", "leading_diamond": " ", "properties": {"fetch_version": false}, "style": "diamond", "template": "{{ if .Error }}{{ .Error }}{{ else }}{{ if .Venv }}{{ .Venv }} {{ end }}{{ .Full }}{{ end }}", "trailing_diamond": "", "type": "python"}, {"background": "#4c1f5e", "foreground": "#7FD5EA", "leading_diamond": " ", "properties": {"fetch_version": false}, "style": "diamond", "template": "{{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }}", "trailing_diamond": "", "type": "go"}, {"background": "#4c1f5e", "foreground": "#42E66C", "leading_diamond": " ", "properties": {"fetch_version": false}, "style": "diamond", "template": "{{ if .PackageManagerIcon }}{{ .PackageManagerIcon }} {{ end }}{{ .Full }}", "trailing_diamond": "", "type": "node"}, {"background": "#4c1f5e", "foreground": "#E64747", "leading_diamond": " ", "properties": {"fetch_version": false}, "style": "diamond", "template": "{{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }}", "trailing_diamond": "", "type": "ruby"}, {"background": "#4c1f5e", "foreground": "#E64747", "leading_diamond": " ", "properties": {"fetch_version": false}, "style": "diamond", "template": "{{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }}", "trailing_diamond": "", "type": "java"}], "type": "rprompt"}, {"alignment": "left", "newline": true, "segments": [{"background": "#4c1f5e", "foreground": "#EFDCF9", "properties": {"time_format": "15:04:05"}, "style": "diamond", "template": " {{ .CurrentDate | date .Format }} ", "trailing_diamond": "", "type": "time"}], "type": "prompt"}], "console_title_template": "{{ .Shell }} - {{ .Folder }}", "final_space": true, "version": 3}