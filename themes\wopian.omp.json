{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "segments": [{"foreground": "cyan", "style": "powerline", "template": "{{ .<PERSON><PERSON> }}", "type": "os"}, {"foreground": "cyan", "properties": {"style": "full"}, "style": "plain", "template": " {{ .Path }} ", "type": "path"}, {"foreground": "light<PERSON>ellow", "style": "plain", "template": ":: <lightBlue>git(</>{{ .HEAD }}<lightBlue>)</>", "type": "git"}], "type": "prompt"}, {"alignment": "right", "segments": [{"foreground": "#68a063", "properties": {"display_mode": "files", "fetch_package_manager": true, "fetch_version": true, "npm_icon": "/npm", "yarn_icon": "/yarn"}, "style": "plain", "template": " {{ if .PackageManagerIcon }}{{ .PackageManagerIcon }} {{ end }}{{ .Full }}", "type": "node"}, {"foreground": "#4063D8", "properties": {"display_mode": "files", "fetch_version": true}, "style": "plain", "template": " {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }}", "type": "crystal"}, {"foreground": "#DE3F24", "properties": {"display_mode": "files", "fetch_version": true}, "style": "plain", "template": " {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }}", "type": "ruby"}, {"foreground": "#FED142", "properties": {"display_mode": "context", "fetch_virtual_env": false}, "style": "plain", "template": " {{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }}", "type": "python"}, {"foreground": "lightGreen", "style": "plain", "template": " {{ .CurrentDate | date .Format }} ", "type": "time"}], "type": "prompt"}, {"alignment": "left", "newline": true, "segments": [{"foreground": "lightGreen", "foreground_templates": ["{{ if gt .Code 0 }}red{{ end }}"], "properties": {"always_enabled": true}, "style": "powerline", "template": "➜ ", "type": "status"}], "type": "prompt"}], "version": 3}