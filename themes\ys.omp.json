{"$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "blocks": [{"alignment": "left", "segments": [{"foreground": "white", "properties": {"fetch_version": false}, "style": "plain", "template": "({{ if .Error }}{{ .Error }}{{ else }}{{ if .Venv }}{{ .Venv }} {{ end }}{{ .Full }}{{ end }})", "type": "python"}], "type": "prompt"}, {"alignment": "left", "newline": true, "segments": [{"foreground": "lightBlue", "style": "plain", "template": "# ", "type": "text"}, {"foreground": "red", "style": "plain", "template": " % ", "type": "root"}, {"style": "plain", "template": "<cyan>{{ .UserName }}</> <darkGray>@</> <green>{{ .HostName }}</> ", "type": "session"}, {"foreground": "light<PERSON>ellow", "properties": {"style": "full"}, "style": "plain", "template": "<darkGray>in </>{{ .Path }} ", "type": "path"}, {"style": "plain", "template": "<darkGray>on</> <white>git:</><cyan>{{ .HEAD }}</>{{ if .Working.Changed }}<red> x</>{{ end }} ", "type": "git", "properties": {"fetch_status": true}}, {"foreground": "<PERSON><PERSON><PERSON>", "style": "plain", "template": "[{{ .CurrentDate | date .Format }}]", "type": "time"}, {"foreground": "red", "style": "plain", "template": " C:{{ if gt .Code 0 }}{{ .Code }}{{ end }} ", "type": "status"}], "type": "prompt"}, {"alignment": "left", "newline": true, "segments": [{"foreground": "lightRed", "style": "plain", "template": "$", "type": "text"}], "type": "prompt"}], "final_space": true, "version": 3}