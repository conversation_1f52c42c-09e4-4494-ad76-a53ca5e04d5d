---
id: block
title: Block
sidebar_label: Block
---

Let's take a closer look at what defines a block.

import Config from "@site/src/components/Config.js";

<Config
  data={{
    blocks: [
      {
        type: "prompt",
        alignment: "left",
        segments: [],
      },
    ],
  }}
/>

## Settings

| Name               | Type      |
| ------------------ | --------- |
| `type`             | `string`  |
| `newline`          | `boolean` |
| `alignment`        | `string`  |
| `filler`           | `string`  |
| `overflow`         | `string`  |
| `leading_diamond`  | `string`  |
| `trailing_diamond` | `string`  |
| `segments`         | `array`   |
| `force`            | `boolean` |
| `index`            | `int`     |

### Type

Tells the engine what to do with the block. There are two options:

- `prompt` renders one or more segments
- `rprompt` renders one or more segments aligned to the right of the cursor. Only one `rprompt` block is permitted.
  Supported on bash (with [ble.sh]), zsh, PowerShell, cmd, nu and fish.

### Newline

Start the block on a new line - defaults to `false`. For `pwsh` and `cmd` this will not print a newline
that's defined on the first block when the prompt is on the first line (when using clear), or when the shell session
starts (1st prompt). To enable the same behavior for `bash` and `zsh`, set `enable_cursor_positioning` to `true`.
This can have the side effect of swallowing commands you type while the prompt loads, or while the current
command is still ongoing. This is a limitation of the shell and not Oh My Posh.

### Alignment

- `left`
- `right`

Tell the engine if the block should be left or right-aligned.

### Filler

When you want to join a right and left aligned block with a repeated set of characters, add the character(s)
to be repeated to this property. Add this property to the _right_ aligned block. This supports the use of
[color overrides][color-overrides].

<Config
  data={{
    block: {
      alignment: "right",
      filler: ".",
    },
  }}
/>

Filler allows you to specify a template to tweak the text used as filler. This template behaves the same as
Segment templates, however, fewer properties are available.

| Name        | Type   | Description                                                           |
| ----------- | ------ | --------------------------------------------------------------------- |
| `.Overflow` | `text` | if no overflow was needed, this is empty. Otherwise `hide` or `break` |
| `.Padding`  | `int`  | the computed length of the padding between left and right blocks      |

This can be very useful if you wish to use a filler text when there is no overflow and use
empty space when the right block is hidden or drawn on a newline due to overflow.

<Config
  data={{
    block: {
      alignment: "right",
      overflow: "hide",
      filler: "{{ if .Overflow }} {{ else }}-{{ end }}",
    },
  }}
/>

### Overflow

- `break`
- `hide`

When the right aligned block is so long it will overflow the left aligned block, the engine will either
break the block or hide it based on the setting. By default it is printed as is on the same line.

### Leading Diamond

The character to use as a leading diamond for the first segment in case you always want to start the block
with the same leading diamond, regardless of which segment is enabled or not.

### Trailing Diamond

The character to use as a trailing diamond for the last segment in case you always want to end the block
with the same trailing diamond, regardless of which segment is enabled or not.

### Segments

Array of one or more [segments][segment].

### Force

When set to `true`, the block will always be rendered, even if all segments are empty. Defaults to `false`.

### Index

The index of the block in the configuration. This is used to [override] a specific block in a base configuration.
This is a 1-based index, so the first block has an index of `1`.

[ble.sh]: https://github.com/akinomyoga/ble.sh
[color-overrides]: /docs/configuration/colors#color-overrides
[segment]: segment.mdx
[override]: /docs/configuration/general#extends
