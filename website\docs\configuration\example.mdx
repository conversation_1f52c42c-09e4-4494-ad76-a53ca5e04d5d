---
id: sample
title: Sample
sidebar_label: Sample
---

import Config from "@site/src/components/Config.js";

<Config
  data={{
    $schema:
      "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json",
    blocks: [
      {
        segments: [
          {
            foreground: "#007ACC",
            template: " {{ .CurrentDate | date .Format }} ",
            properties: {
              time_format: "15:04:05",
            },
            style: "plain",
            type: "time",
          },
        ],
        type: "rprompt",
      },
      {
        alignment: "left",
        newline: true,
        segments: [
          {
            background: "#ffb300",
            foreground: "#ffffff",
            leading_diamond: "\ue0b6",
            template: " {{ .UserName }} ",
            style: "diamond",
            trailing_diamond: "\ue0b0",
            type: "session",
          },
          {
            background: "#61AFEF",
            foreground: "#ffffff",
            powerline_symbol: "\ue0b0",
            template: " {{ .Path }} ",
            properties: {
              style: "folder",
            },
            exclude_folders: ["/super/secret/project"],
            style: "powerline",
            type: "path",
          },
          {
            background: "#2e9599",
            background_templates: [
              "{{ if or (.Working.Changed) (.Staging.Changed) }}#f36943{{ end }}",
              "{{ if and (gt .Ahead 0) (gt .Behind 0) }}#a8216b{{ end }}",
              "{{ if gt .Ahead 0 }}#35b5ff{{ end }}",
              "{{ if gt .Behind 0 }}#f89cfa{{ end }}",
            ],
            foreground: "#193549",
            foreground_templates: [
              "{{ if and (gt .Ahead 0) (gt .Behind 0) }}#ffffff{{ end }}",
            ],
            powerline_symbol: "\ue0b0",
            template:
              " {{ .HEAD }}{{if .BranchStatus }} {{ .BranchStatus }}{{ end }} ",
            properties: {
              branch_template: "{{ trunc 25 .Branch }}",
              fetch_status: true,
            },
            style: "powerline",
            type: "git",
          },
          {
            background: "#00897b",
            background_templates: ["{{ if gt .Code 0 }}#e91e63{{ end }}"],
            foreground: "#ffffff",
            template: "<parentBackground>\ue0b0</> \ue23a ",
            properties: {
              always_enabled: true,
            },
            style: "diamond",
            trailing_diamond: "\ue0b4",
            type: "status",
          },
        ],
        type: "prompt",
      },
    ],
    final_space: true,
    version: 3,
  }}
/>
