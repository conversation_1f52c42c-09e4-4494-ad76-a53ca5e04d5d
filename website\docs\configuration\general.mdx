---
id: general
title: General
sidebar_label: General
---

import Tabs from "@theme/Tabs";
import TabItem from "@theme/TabItem";

Oh My Posh renders your prompt based on the definition of _blocks_ (like Lego) which contain one or more _segments_.
A really simple configuration could look like this. The default format is `json`, but we also support `toml` and `yaml`.
There's a [schema][schema] available which is kept up-to-date and helps with autocomplete and validation of the configuration.

:::info
There are a few [themes][themes] available which are basically predefined configurations. You can use these as they are, or as a
starting point to create your own configuration.
:::

<Tabs
  defaultValue="json"
  groupId="sample"
  values={[
    { label: 'json', value: 'json', },
    { label: 'yaml', value: 'yaml', },
    { label: 'toml', value: 'toml', },
  ]
}>
<TabItem value="json">

```json
{
  "$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json",
  "final_space": true,
  "version": 3,
  "blocks": [
    {
      "type": "prompt",
      "alignment": "left",
      "segments": [
        {
          "type": "path",
          "style": "powerline",
          "powerline_symbol": "\uE0B0",
          "foreground": "#ffffff",
          "background": "#61AFEF",
          "properties": {
            "style": "folder"
          }
        }
      ]
    }
  ]
}
```

</TabItem>
<TabItem value="yaml">

```yaml
# yaml-language-server: $schema=https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json
final_space: true
version: 3
blocks:
  - type: prompt
    alignment: left
    segments:
      - type: path
        style: powerline
        powerline_symbol: ""
        foreground: "#ffffff"
        background: "#61AFEF"
        properties:
          style: folder
```

</TabItem>
<TabItem value="toml">

```toml
"$schema" = "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json"
final_space = true
version = 3

[[blocks]]
type = "prompt"
alignment = "left"

  [[blocks.segments]]
  type = "path"
  style = "powerline"
  powerline_symbol = ""
  foreground = "#ffffff"
  background = "#61AFEF"

    [blocks.segments.properties]
    style = "folder"
```

</TabItem>
</Tabs>

With this configuration, a single powerline segment is rendered that shows the name of the folder you're currently in.
To set this configuration in combination with a Oh My Posh [executable][releases], use the `--config` flag to
set a path to a file containing the above code. The `--shell universal` flag is used to print the prompt without
escape characters to see the prompt as it would be shown inside a prompt function for your shell.

:::caution
The command below will not persist the configuration for your shell but print the prompt in your terminal.
If you want to use your own configuration permanently, adjust the prompt configuration to use your custom
configuration.
:::

```bash
oh-my-posh print primary --config sample.json --shell uni
```

If all goes according to plan, you should see the prompt being printed out on the line below. In case you see a lot of
boxes with question marks, set up your terminal to use a [supported font][font] before continuing.

:::tip
The `--config` flag can accept either a local filepath or a remotely hosted config file.

For example, the following is a valid `--config` flag:
`--config 'https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/jandedobbeleer.omp.json'`
:::

## Settings

| Name                        | Type             | Default | Description                                                                                                                                                                                                                                                                  |
| --------------------------- | ---------------- | ------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `final_space`               | `boolean`        |         | when true adds a space at the end of the prompt                                                                                                                                                                                                                              |
| `pwd`                       | `string`         |         | notify terminal of current working directory, values can be `osc99`, `osc7` or `osc51` depending on your terminal. Supports [templates][templates]                                                                                                                           |
| `terminal_background`       | `string`         |         | [color][colors] - terminal background color, set to your terminal's background color when you notice black elements in Windows Terminal or the Visual Studio Code integrated terminal                                                                                        |
| `accent_color`              | `string`         |         | [color][colors] - accent color, used as a fallback when the `accent` [color][accent] is not supported                                                                                                                                                                        |
| `var`                       | `map[string]any` |         | config variables to use in [templates][templates]. Can be any value                                                                                                                                                                                                          |
| `shell_integration`         | `boolean`        | `false` | enable shell integration using FinalTerm's OSC sequences. Works in bash, cmd (Clink v1.14.25+), fish, powershell and zsh                                                                                                                                                     |
| `enable_cursor_positioning` | `boolean`        | `false` | enable fetching the cursor position in bash and zsh to allow automatic hiding of leading newlines when at the top of the shell                                                                                                                                               |
| `patch_pwsh_bleed`          | `boolean`        | `false` | patch a PowerShell bug where the background colors bleed into the next line at the end of the buffer (can be removed when [this][pwsh-bleed] is merged)                                                                                                                      |
| `upgrade`                   | `Upgrade`        |         | enable auto upgrade or the upgrade notice. See [Upgrade]                                                                                                                                                                                                                     |
| `iterm_features`            | `[]string`       | `false` | enable iTerm2 specific features:<ul><li>`prompt_mark`: add the `iterm2_prompt_mark` [function][iterm2-si] for supported shells</li><li>`current_dir`: expose the current directory for iTerm2</li><li>`remote_host`: expose the current remote and user for iTerm2</li></ul> |
| `maps`                      | [`Maps`](#maps)  |         | a list of custom text mappings                                                                                                                                                                                                                                               |
| `async`                     | `boolean`        | `false` | load the prompt async. Will either load the standard prompt, or allow you to start typing right away. Supperted for `pwsh`, `powershell`, `zsh`, `bash` and `fish`                                                                                                           |
| `version`                   | `int`            | `3`     | the config version, currently at `3`                                                                                                                                                                                                                                         |
| `extends`                   | `string`         |         | the configuration to [extend] from                                                                                                                                                                                                                                           |

### Maps

| Name         | Type     | Description                              |
| ------------ | -------- | ---------------------------------------- |
| `user_name`  | `object` | text replacement mapping for user names  |
| `host_name`  | `object` | text replacement mapping for host names  |
| `shell_name` | `object` | text replacement mapping for shell names |

import Config from "@site/src/components/Config.js";

<Config
  data={{
    maps: {
      user_name: {
        jan: "🚀",
        root: "⚡",
      },
      host_name: {
        laptop123: "work",
      },
    },
  }}
/>

### Extends

The `extends` key allows you to extend an existing configuration. This is useful when you want to build upon a base configuration without
duplicating all the settings like a [theme][themes]. The value should be a path to another configuration file, which can be either local or remote.

Replacing values is done by repeating the value in the new configuration. For example, if you want to change the `accent_color` in a base configuration,
you can do so by specifying the `accent_color` key in your new configuration. The new value will override the one in the base configuration.

To override segments and blocks, specify the block using the same `type` and `alignment` as the base configuration. You can then add or modify the segments
within that block. Segments are matched by their `alias` or `type`, so you can add new segments or modify existing ones without affecting the rest of the configuration.

For more advanced use cases, you can also specify the index of the `block` or `segment` you want to override. This allows you to override `blocks` or `segments` at
specific positions in the configuration. Be aware that the index is **1-based**, so the first `block` or `segment` has an index of `1`.

### JSON Schema Validation

As mentioned above, Oh My Posh configurations can utilize JSON Schema to validate their contents. Configurations should include a link to
the [external schema document][schema] which prescribes the appropriate structure and contents for various elements. If
your code editor is configured to use JSON Schema, it will compare your configuration to the external document, and issue
warnings for discrepancies.

For example, given the following code:

```json
...
"segments": [
  {
    "type": "an_invalid_entry",
    "template": "{{ if gt .Code 0 }}\uf134{{ end }}",
  }
]
...
```

Warnings will be raised for type, since `an_invalid_entry` is not in the list of acceptable values, as well as
for the entire segment item (enclosed in `{}`), since it lacks the required `style` key. Take advantage of these
warnings, and ignore them at your peril.

### Accepted Formats

Oh My Posh supports three file formats for configurations: `json`, `yaml`, and `toml`.

Various converters exist to convert between these, although they aren't perfect and will require manual
adjustment. Notably, the schema implementation for json is as follows:

```json
{
  "$schema": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json"
}
```

While for yaml:

```yaml
# yaml-language-server: $schema=https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json
```

Converters won't catch this change, so you will need to adjust manually.

[releases]: https://github.com/JanDeDobbeleer/oh-my-posh/releases/latest
[font]: /docs/installation/fonts
[schema]: https://github.com/JanDeDobbeleer/oh-my-posh/blob/main/themes/schema.json
[themes]: https://github.com/JanDeDobbeleer/oh-my-posh/tree/main/themes
[colors]: /docs/configuration/colors
[accent]: /docs/configuration/colors#standard-colors
[templates]: /docs/configuration/templates#config-variables
[pwsh-bleed]: https://github.com/PowerShell/PowerShell/pull/19019
[iterm2-si]: https://iterm2.com/documentation-shell-integration.html
[Upgrade]: /docs/installation/upgrade
[extend]: /docs/configuration/general#extends
