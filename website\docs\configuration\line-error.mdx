---
id: line-error
title: Line error
sidebar_label: Line error
---

import Config from "@site/src/components/Config.js";

:::info
This feature only works in `powershell` for the time being.
:::

Line error, when enabled, replaces the last part of the prompt when the text entered is invalid. It leverages
[PSReadLine's][psreadline] `-PromptText` setting by adding two distinct prompts. One for a valid line,
and one for when there's an error. As PSReadLine will rewrite the last part of
your prompt with the value of either based on the line's context, you will need to make sure everything
is compatible with your config as **these values are only set once** on shell start.

There are two config settings you need to tweak:

- `valid_line`: displays when the line is valid (again)
- `error_line`: displays when the line is faulty

You can use go [text/template][go-text-template] templates extended with [sprig][sprig] to enrich the text.

## Configuration

You need to extend or create a custom theme with your prompt overrides. For example:

<Config
  data={{
    valid_line: {
      background: "transparent",
      foreground: "#ffffff",
      template: "<#e0def4,#286983>\uf42e </><#286983,transparent>\ue0b4</> ",
    },
    error_line: {
      background: "transparent",
      foreground: "#ffffff",
      template: "<#eb6f92,#286983>\ue009 </><#286983,transparent>\ue0b4</> ",
    },
  }}
/>

## Properties

| Name         | Type     | Description                                                |
| ------------ | -------- | ---------------------------------------------------------- |
| `background` | `string` | [color][colors]                                            |
| `foreground` | `string` | [color][colors]                                            |
| `template`   | `string` | a fully featured [template][templates] - defaults to empty |

[colors]: /docs/configuration/colors
[go-text-template]: https://golang.org/pkg/text/template/
[sprig]: https://masterminds.github.io/sprig/
[console-title]: /docs/configuration/title#console-title-template
[psreadline]: https://github.com/PowerShell/PSReadLine
[templates]: /docs/configuration/templates
[cstp]: /docs/configuration/templates#cross-segment-template-properties
