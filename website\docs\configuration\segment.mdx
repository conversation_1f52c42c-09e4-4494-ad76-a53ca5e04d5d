---
id: segment
title: Segment
sidebar_label: Segment
---

import Config from "@site/src/components/Config.js";

A segment is a part of the prompt with a certain context. There are different types available out-of-the-box, if you're
looking for what's included, feel free to skip this part and browse through the [segments][segments]. Keep reading to
understand how to configure a segment.

<Config
  data={{
    blocks: [
      {
        segments: [
          {
            type: "path",
            style: "powerline",
            powerline_symbol: "\uE0B0",
            foreground: "#ffffff",
            background: "#61AFEF",
            template: " {{ .Path }} ",
            include_folders: ["/Users/<USER>/Projects"],
          },
        ],
      },
    ],
  }}
/>

## Settings

| Name                       | Type         | Description                                                                                                                                                                                                                                                                                                                |
| -------------------------- | ------------ | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `type`                     | `string`     | takes the `string` value referencing which segment logic it needs to run (see [segments][segments] for possible values)                                                                                                                                                                                                    |
| `style`                    | `string`     | see [Style][style] below. Possible values:<ul><li>`powerline`</li><li>`plain`</li><li>`diamond`</li><li>`accordion`</li><li>a go [text/template][go-text-template] [template][templates] resolving to the above values</li></ul>                                                                                           |
| `powerline_symbol`         | `string`     | character to use when `"style": "powerline"`                                                                                                                                                                                                                                                                               |
| `leading_powerline_symbol` | `string`     | when `"style": "powerline"` we use an ANSI hack to invert the `powerline_symbol` to create a transparent glyph. This gives the best alignment, but might not work in every terminal. If you see black elements at the start of powerline segments, you can set this to the "opening" version of the `powerline_symbol`.    |
| `invert_powerline`         | `boolean`    | if `true` swaps the foreground and background colors. Can be useful when the character you want does not exist in the perfectly mirrored variant for example - defaults to `false`                                                                                                                                         |
| `leading_diamond`          | `string`     | character to use at the start of the segment. Will take the background color of the segment as its foreground color                                                                                                                                                                                                        |
| `trailing_diamond`         | `string`     | character to use at the end of the segment. Will take the background color of the segment as its foreground color                                                                                                                                                                                                          |
| `foreground`               | `string`     | [color][colors]                                                                                                                                                                                                                                                                                                            |
| `foreground_templates`     | `[]Template` | [color templates][color-templates]                                                                                                                                                                                                                                                                                         |
| `background`               | `string`     | [color][colors]                                                                                                                                                                                                                                                                                                            |
| `background_templates`     | `[]Template` | [color templates][color-templates]                                                                                                                                                                                                                                                                                         |
| `template`                 | `string`     | a go [text/template][go-text-template] [template][templates] to render the prompt                                                                                                                                                                                                                                          |
| `templates`                | `[]Template` | in some cases having a single [template][templates] string is a bit cumbersome. Templates allows you to span the segment's [template][templates] string multiple lines where every [template][templates] is evaluated and depending on what you aim to achieve, there are two possible outcomes based on `templates_logic` |
| `templates_logic`          | `string`     | <ul><li>`first_match`: return the first non-whitespace string and skip everything else</li><li>`join`:evaluate all templates and join all non-whitespace strings (**default**)</li></ul>                                                                                                                                   |
| `properties`               | `[]Property` | see [Properties][properties] below                                                                                                                                                                                                                                                                                         |
| `interactive`              | `boolean`    | when true, the segment text is not escaped to allow the use of interactive prompt escape sequences in Bash/Zsh - defaults to `false`                                                                                                                                                                                       |
| `alias`                    | `string`     | for use with [cross segment template properties][cstp]                                                                                                                                                                                                                                                                     |
| `min_width`                | `int`        | if the terminal width is smaller than this value, the segment will be hidden. For your terminal width, see `oh-my-posh get width`. Defaults to `0` (disable)                                                                                                                                                               |
| `max_width`                | `int`        | if the terminal width exceeds this value, the segment will be hidden. For your terminal width, see `oh-my-posh get width`. Defaults to `0` (disable)                                                                                                                                                                       |
| `cache`                    | `Cache`      | how to cache the segment to avoid fetching information too much, see [below][cache]                                                                                                                                                                                                                                        |
| `include_folders`          | `[]string`   | define which folders to include to enable the segment, see [below][include-exclude]                                                                                                                                                                                                                                        |
| `exclude_folders`          | `[]string`   | define which folders to exclude to disable the segment, see [below][include-exclude]                                                                                                                                                                                                                                       |
| `force`                    | `boolean`    | when true, the segment is always rendered, even when it's only whitespace - defaults to `false`                                                                                                                                                                                                                            |
| `index`                    | `int`        | used to [override] a specific segment (1-based)                                                                                                                                                                                                                                                                            |

:::warning
In Bash/Zsh, when the property `interactive` is `true` for a segment, the prompt length calculation can be wrong
because of possible string expansions (e.g., `\w` in Bash and `%d` in Zsh which both expand to the current working
directory), thus a right-aligned block is not being properly right-aligned.

Unfortunately, it's not possible for Oh My Posh to know the final prompt length since the string expansion is done
by your shell, so use this at your own risk.
:::

## Style

Style defines how a prompt is rendered. Looking at the most prompt
themes out there, we identified 4 types. All of these require a different configuration and depending on the look
you want to achieve you might need to understand/use them all.

### Powerline

What started it all for us. Makes use of a single symbol (`powerline_symbol`) to separate the segments. It takes the
background color of the previous segment (or transparent if none) and the foreground of the current one (or transparent
if we're at the last segment). Expects segments to have a colored background, else there little use for this one.

When you see black triangles (or other characters depending on the `powerline_symbol` you use) at the start of a segment,
you can set `leading_powerline_symbol` to the "opening" version of the `powerline_symbol`.
This will not use the inverted ANSI hack we have in place as that's not supported in every terminal. You might need to tweak
your font settings to get the best alignment.

:::warning Git Bash
The `powerline` style has issues in Git Bash due to the icons width being incorrectly calculated, rendering
the prompt broken when typing long commands or searching the history.

The following prompt configuration has the same problem:

```bash
export PS1=" "
```

:::

### Plain

Simple. Colored text on a transparent background. Make sure to set `foreground` for maximum enjoyment.

### Diamond

While Powerline works great with a single symbol, sometimes you want a segment to have a different start and end symbol.
Just like a diamond: `< my segment text >`. The difference between this and plain is that the diamond symbols take the
segment background as their foreground color.

### Accordion

Same as Powerline except that it will display even when disabled, but without text. That way it seems
as if the segment is not expanded, just like an accordion.

## Properties

The `properties` attribute allows you to customize the behavior and appearance of a segment beyond the default settings.
These options can be anything from feature flags, configuration or additional styling details that will only apply to the segment in question.
To understand the available properties for a particular segment, refer to its corresponding documentation section where each property is explained in detail.

## Cache

The cache property allows you to control how often a segment is refreshed. This is useful when a segment is slow to
generate or when you want to avoid fetching information too often. The cache property is an object with the following
properties:

| Name       | Type     | Description                                                                                                                                                                                                                       |
| ---------- | -------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `duration` | `string` | the duration for which the segment will be cached. The duration is a string in the format `1h2m3s`. The duration is parsed using the [time.ParseDuration] function from the Go standard library. To disable the cache, use `none` |
| `strategy` | `string` | the strategy to use to identify if we should show the segment's cache value. See below for more information on strategy                                                                                                           |

<Config
  data={{
    cache: {
      duration: "1h",
      strategy: "folder",
    },
  }}
/>

### Strategy

#### Folder

The folder strategy will cache the segment based on the current working directory. It will cache a separate value for each
directory. This is useful when you for example work with language segments, or source control segments. The source control
segments understand repository context in this case, so the same segment cache value is used when in a git repository,
regardless of the folder you're in.

#### Session

The session strategy will cache the segment based on the current shell session. Use this for segments you want to display at all times
but don't want to refresh too often.

## Include / Exclude Folders

Sometimes you might want to have a segment only rendered in certain folders. If `include_folders` is specified,
the segment will only be rendered when in one of those locations. If `exclude_folders` is specified, the segment
will not be rendered when in one of the excluded locations.

<Config
  data={{
    include_folders: ["/Users/<USER>/Projects"],
  }}
/>

<Config
  data={{
    exclude_folders: ["/Users/<USER>/Projects"],
  }}
/>

The strings specified in these properties are evaluated as [regular expressions][regex]. You
can use any valid regular expression construct, but the regular expression must match the **ENTIRE** directory
name. The following will match `/Users/<USER>/Projects/Foo` but not `/home/<USER>/<USER>/Projects/Foo`.

<Config
  data={{
    include_folders: ["/Users/<USER>/Projects/.*"],
  }}
/>

You can also combine these properties:

<Config
  data={{
    include_folders: ["/Users/<USER>/Projects/.*"],
    exclude_folders: ["/Users/<USER>/Projects/secret-project.*"],
  }}
/>

:::info

- Oh My Posh will accept both `/` and `\` as path separators for a folder and will match regardless of which
  is used by the current operating system.
- Because the strings are evaluated as regular expressions, if you want to use a backslash (`\`) in a Windows
  directory name, you need to specify it as double backslashes, and if using JSON format you should escape it as `\\\\`.
- The character `~` at the start of a specified folder will match the user's home directory.
- The comparison is case-insensitive on Windows and macOS, but case-sensitive on other operating systems.

This means that for user Bill, who has a user account `Bill` on Windows and `bill` on Linux, `~/Foo` might match
`C:\Users\<USER>\Foo` or `C:\Users\<USER>\foo` on Windows but only `/home/<USER>/Foo` on Linux.
:::

## Index

The index of the segment in the configuration. This is used to [override] a specific segment in a base configuration.
This is a 1-based index, so the first segment has an index of `1`.

## Hiding segments

### Conditionally

To hide a whole segment including the leading and trailing symbol based on a [template][templates], the template must
**evaluate to an empty string**. This can be achieved with conditional statements (`if`). The example below will render
a diamond segment, only if the environment variable `POSH_ENV` is not empty.

Only spaces are excluded, meaning you can still add line breaks and tabs if that is something you're after.

<Config
  data={{
    type: "text",
    style: "diamond",
    leading_diamond: " \ue0b6",
    trailing_diamond: "\ue0b4",
    foreground: "#ffffff",
    background: "#d53c14",
    template: "{{ if .Env.POSH_ENV }} \uf8c5 {{ .Env.POSH_ENV }} {{ end }}",
  }}
/>

### On the fly

Sometimes run into a situation where you don't want to see a specific segment but the use-case does not justify
using a conditional template. In this case you can use the `oh-my-posh toggle <type>` command to toggle the
segment on or off. This works on a **per shell session basis**, meaning that if you toggle a segment off in one instance
of a shell, it will not disable in the others.

To list the currently toggled segments, use `oh-my-posh get toggles`.

[segments]: /docs/segments/cli/angular
[properties]: #properties
[style]: #style
[colors]: /docs/configuration/colors
[go-text-template]: https://golang.org/pkg/text/template/
[sprig]: https://masterminds.github.io/sprig/
[regex]: https://www.regular-expressions.info/tutorial.html
[templates]: templates.mdx
[color-templates]: /docs/configuration/colors#color-templates
[cstp]: templates.mdx#cross-segment-template-properties
[cache]: #cache
[include-exclude]: #include--exclude-folders
[time.ParseDuration]: https://golang.org/pkg/time/#ParseDuration
[override]: /docs/configuration/general#extends
