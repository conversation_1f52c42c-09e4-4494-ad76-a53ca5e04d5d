---
id: templates
title: Templates
sidebar_label: Templates
---

import Tabs from "@theme/Tabs";
import TabItem from "@theme/TabItem";
import Config from "@site/src/components/Config.js";

Every segment has a `template` property to tweak the text that is displayed.
Under the hood, this uses go's [text/template][go-text-template] feature extended with [sprig][sprig] and
offers a few standard properties to work with.

## Global properties

These properties can be used anywhere, in any segment. If a segment contains a property with the same name,
the segment property value will be used instead. In case you want to use the global property, you can prefix
it with `.$` to reference it directly.

| Name            | Type                  | Description                                                                 |
| --------------- | --------------------- | --------------------------------------------------------------------------- |
| `.Root`         | `boolean`             | is the current user root/admin or not                                       |
| `.PWD`          | `string`              | the current working directory (`~` for `$HOME`)                             |
| `.AbsolutePWD`  | `string`              | the current working directory (unaltered)                                   |
| `.PSWD`         | `string`              | the current non-filesystem working directory in PowerShell                  |
| `.Folder`       | `string`              | the current working folder                                                  |
| `.Shell`        | `string`              | the current shell name                                                      |
| `.ShellVersion` | `string`              | the current shell version                                                   |
| `.SHLVL`        | `int`                 | the current shell level                                                     |
| `.UserName`     | `string`              | the current user name                                                       |
| `.HostName`     | `string`              | the host name                                                               |
| `.Code`         | `int`                 | the last exit code                                                          |
| `.Jobs`         | `int`                 | number of background jobs (only available for zsh, PowerShell, and Nushell) |
| `.OS`           | `string`              | the operating system                                                        |
| `.WSL`          | `boolean`             | in WSL yes/no                                                               |
| `.Templates`    | `string`              | the [templates][templates] result                                           |
| `.PromptCount`  | `int`                 | the prompt counter, increments with 1 for every prompt invocation           |
| `.Version`      | `string`              | the Oh My Posh version                                                      |
| `.Segment`      | [`Segment`](#segment) | the current segment's metadata                                              |

### Segment

| Name             | Type     | Description                               |
| ---------------- | -------- | ----------------------------------------- |
| `.Segment.Index` | `int`    | the current segment's index (as rendered) |
| `.Segment.Text`  | `string` | the segment's rendered text               |

## Environment variables

| Name           | Type     | Description                                                               |
| -------------- | -------- | ------------------------------------------------------------------------- |
| `.Env.VarName` | `string` | Any environment variable where `VarName` is the environment variable name |

:::tip
For the shells below, you can override a function to execute some logic before the prompt is rendered.
This can be used to for example populate an environment variable with a specific context.

<Tabs
  defaultValue="powershell"
  groupId="shell"
  values={[
    { label: 'powershell', value: 'powershell', },
    { label: 'zsh', value: 'zsh', },
    { label: 'bash', value: 'bash', },
    { label: 'fish', value: 'fish', },
    { label: 'nu', value: 'nu', },
  ]
}>
<TabItem value="powershell">

```powershell
function Set-EnvVar([bool]$originalStatus) {
    $env:POSH=$(Get-Date)
}
New-Alias -Name 'Set-PoshContext' -Value 'Set-EnvVar' -Scope Global -Force
```

</TabItem>
<TabItem value="zsh">

```bash
function set_poshcontext() {
    export POSH=$(date)
}
```

</TabItem>
<TabItem value="bash">

```bash
function set_poshcontext() {
    export POSH=$(date)
}
```

</TabItem>
<TabItem value="fish">

```shell
function set_poshcontext
  set --export POSH $(date)
end
```

</TabItem>
<TabItem value="nu">

```bash
$env.SET_POSHCONTEXT = {
	$env.POSH = ( date now );
}
```

</TabItem>
</Tabs>

:::

## Config variables

| Name           | Type  | Description                                              |
| -------------- | ----- | -------------------------------------------------------- |
| `.Var.VarName` | `any` | Any config variable where `VarName` is the variable name |

### Example

<Config
  data={{
    version: 3,
    // highlight-start
    var: {
      Hello: "hello",
      World: "world",
    },
    // highlight-end
    blocks: [
      {
        type: "prompt",
        alignment: "left",
        segments: [
          {
            type: "text",
            style: "plain",
            foreground: "p:white",
            // highlight-next-line
            template: "{{ .Var.Hello }} {{ .Var.World }} ",
          },
        ],
      },
    ],
  }}
/>

## Template logic

<!--  markdownlint-disable MD013 -->

| Template                                                             | Description                                                                                                                                                                                                                                                                                                     |
| -------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `{{.}}`                                                              | Root element.                                                                                                                                                                                                                                                                                                   |
| `{{.Var}}`                                                           | Variable in a struct, where Var is a variable.                                                                                                                                                                                                                                                                  |
| `{{- .Var -}}`                                                       | Remove extra white space surrounding the .Var variable and remove the newline. Either side is fine too.                                                                                                                                                                                                         |
| `{{ $planet := "Earth"}}`                                            | `{{ $planet }}` Store a value in a custom variable to reference later. Note that .$ is used to reference the global/parent context, like in the full example below with $.                                                                                                                                      |
| `Hi {{if .Name}} {{.Name}} {{else}} visitor {{end}}`                 | If-else statement. If will evaluate whether or not the argument is empty. Using the elseif conditional is also an option. The not negation is available too.                                                                                                                                                    |
| `{{if and .Arg1 .Arg2}} both complete. {{else}} incomplete. {{end}}` | The and function compares multiple arguments to return the boolean AND (if arg1 then arg2 else arg1). Both arguments are evaluated. The or function compares multiple arguments to return the boolean OR. Similar to if arg1 then arg1 else arg2, so arg2 will never be evaluated if arg1 is false (not empty). |
| `{{with .Var}} {{end}}`                                              | With statement, where Var is a variable. It skips the block if the variable is absent.                                                                                                                                                                                                                          |
| `{{range .Array}} {{end}}`                                           | Range statement, where Array is an array, slice, map, or channel.                                                                                                                                                                                                                                               |
| `{{ lt 3 4 }}`                                                       | This lt comparison function evaluates to true since 3 is less than 4 (other boolean operators: eq, ne, lt, le, gt, ge).                                                                                                                                                                                         |

<!-- markdownlint-enable MD013 -->

## Helper functions

### Sprig

Oh My Posh has all [sprig][sprig] functions included, meaning you can do operations on strings, paths and a lot of other
manipulations straight from your template. Have a look at [their documentation][sprig] for available options and how to
use them.

### Custom

<!-- markdownlint-disable MD013 -->

| Template                                                           | Description                                                                                                                |
| ------------------------------------------------------------------ | -------------------------------------------------------------------------------------------------------------------------- |
| `{{ url .UpstreamIcon .UpstreamURL }}`                             | Create an `OSC8` hyperlink to a website to open your default browser (needs terminal [support][terminal-list-hyperlinks]). |
| `{{ path .Path .Location }}`                                       | Create an `OSC8` file link to a folder to open your file explorer (needs terminal [support][terminal-list-hyperlinks]).    |
| `{{ secondsRound 3600 }}`                                          | Round seconds to a time indication. In this case the output is `1h`.                                                       |
| `{{ if glob "*.go" }}OK{{ else }}NOK{{ end }}`                     | Exposes [filepath.Glob][glob] as a boolean template function.                                                              |
| `{{ if matchP ".*\\.Repo$" .Path }}Repo{{ else }}No Repo{{ end }}` | Exposes [regexp.MatchString][regexpms] as a boolean template function.                                                     |
| `{{ replaceP "c.t" "cut code cat" "dog" }}`                        | Exposes [regexp.ReplaceAllString][regexpra] as a string template function.                                                 |
| <code>\{\{ .Code &vert; hresult \}\}</code>                        | Transform a status code to its HRESULT value for easy troubleshooting. For example `-1978335212` becomes `0x8A150014`.     |
| `{{ readFile ".version.json" }}`                                   | Read a file in the current directory. Returns a string.                                                                    |
| `{{ random (list \"a\" 2 .MyThirdItem) }}`                         | Selects a random element from a list. The list can be an array or slice containing any types (use sprig's `list`).         |

<!-- markdownlint-enable MD013 -->

## Cross segment template properties

To use another segment's template properties in a template, you can make use of `{{ .Segments.Segment }}`
in your template where `.Segment` is the name of the segment you want to use with the first letter uppercased.

If you want to for example use the [git][git] segment's `.UpstreamGone` property in the [status][status] segment, you can
do so like this:

<Config
  data={{
    template:
      " {{ if .Segments.Git.UpstreamGone }}\ueb05{{ else if gt .Code 0 }}\uf00d{{ else }}\uf00c{{ end }} ",
  }}
/>

:::caution
For this to work, the segment you refer to needs to be in your config. The above example won't work if
your config does not contain a git segment as Oh My Posh only populates the properties when it needs to.
:::

:::tip
If you have two identical segments for a different purpose, you can make use of the `alias` property on the segment
to distinct between both.
:::

<Config
  data={{
    segments: [
      {
        type: "command",
        alias: "Hello",
        style: "plain",
        foreground: "#ffffff",
        properties: {
          command: "echo Hello",
        },
      },
      {
        type: "command",
        alias: "World",
        style: "plain",
        foreground: "#ffffff",
        properties: {
          command: "echo World",
        },
      },
      {
        type: "text",
        style: "plain",
        foreground: "#ffffff",
        template: "{{ .Segments.Hello.Output }} {{ .Segments.World.Output }}",
      },
    ],
  }}
/>

If you want to know if a specific segment is active, you can use the `.Segments.Contains` function, for example:

<Config
  data={{
    template:
      '{{ if .Segments.Contains "Git" }}\ueb05{{ else if gt .Code 0 }}\uf00d{{ else }}\uf00c{{ end }} ',
  }}
/>

## Text decoration

You can make use of the following syntax to decorate text:

| Syntax                 | Description                           |
| ---------------------- | ------------------------------------- |
| `<b>bold</b>`          | `bold` as bold text                   |
| `<u>underline</u>`     | `underline` as underlined text        |
| `<o>overline</o>`      | `overline` as overlined text          |
| `<i>italic</i>`        | `italic` as italic text               |
| `<s>strikethrough</s>` | `strikethrough` as strikethrough text |
| `<d>dimmed</d>`        | `dimmed` as dimmed text               |
| `<f>blink</f>`         | `blink` as blinking (flashing) text   |
| `<r>reversed</r>`      | `reversed` as reversed text           |

This can be used in templates and icons/text inside your config.

[terminal-list-hyperlinks]: https://gist.github.com/egmontkob/eb114294efbcd5adb1944c9f3cb5feda
[path-segment]: /docs/path
[git-segment]: /docs/git
[go-text-template]: https://pkg.go.dev/text/template
[sprig]: https://masterminds.github.io/sprig/
[glob]: https://pkg.go.dev/path/filepath#Glob
[git]: /docs/segments/scm/git
[status]: /docs/segments/system/status
[templates]: /docs/configuration/segment
[regexpms]: https://pkg.go.dev/regexp#Regexp.MatchString
[regexpra]: https://pkg.go.dev/regexp#Regexp.ReplaceAllString
