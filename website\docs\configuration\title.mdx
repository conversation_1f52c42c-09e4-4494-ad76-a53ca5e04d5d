---
id: title
title: Console title
sidebar_label: Console title
---

import Config from "@site/src/components/Config.js";

<Config
  data={{
    console_title_template:
      "{{.Folder}}{{if .Root}} :: root{{end}} :: {{.Shell}}",
  }}
/>

### Console Title Template

The following examples illustrate possible contents for `console_title_template`, provided
the current working directory is `/usr/home/<USER>

To learn more about templates and their possibilities, have a look at the [template][templates] section.

```json
{
  "console_title_template": "{{.Folder}}{{if .Root}} :: root{{end}} :: {{.Shell}}",
  // outputs:
  // when root == false: omp :: zsh
  // when root == true: omp :: root :: zsh
  "console_title_template": "{{.Folder}}", // outputs: omp
  "console_title_template": "{{.Shell}} in {{.PWD}}", // outputs: zsh in /usr/home/<USER>
  "console_title_template": "{{.UserName}}@{{.HostName}} {{.Shell}} in {{.PWD}}", // outputs: MyUser@MyMachine zsh in /usr/home/<USER>
  "console_title_template": "{{.Env.USERDOMAIN}} {{.Shell}} in {{.PWD}}" // outputs: MyCompany zsh in /usr/home/<USER>
}
```

[sprig]: https://masterminds.github.io/sprig/
[templates]: /docs/configuration/templates
