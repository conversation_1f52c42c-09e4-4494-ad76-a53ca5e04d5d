---
id: transient
title: Transient prompt
sidebar_label: Transient prompt
---

import Tabs from "@theme/Tabs";
import TabItem from "@theme/TabItem";
import Config from "@site/src/components/Config.js";

:::info
This feature only works in `nu`, `fish`, `zsh`, `powershell` (`ConstrainedLanguage` mode unsupported), bash (with [ble.sh]) and `cmd` for the time being.
:::

Transient prompt, when enabled, replaces the prompt with a simpler one to allow more screen real estate.
You can use go [text/template][go-text-template] templates extended with [sprig] to enrich the text.
All [template][templates] functionality is available, even reusing [cross segment template properties][cstp] from
the previous primary prompt run.

Typically, your prompt will simply leave the prompt on the screen when you execute a command (or press enter) like so:

![Before Transient](/img/transient-before.gif)

By enabling Transient Prompt, you can replace the prompt with some other content for a cleaner console as shown here:

![After Transient](/img/transient-after.gif)

## Configuration

You need to extend or create a custom theme with your transient prompt. For example:

<Config
  data={{
    transient_prompt: {
      background: "transparent",
      foreground: "#ffffff",
      template: "{{ .Shell }}> ",
    },
  }}
/>

## Properties

| Name                   | Type      | Description                                                                                                                                    |
| ---------------------- | --------- | ---------------------------------------------------------------------------------------------------------------------------------------------- |
| `foreground`           | `string`  | [color][colors]                                                                                                                                |
| `foreground_templates` | `array`   | [color templates][color-templates]                                                                                                             |
| `background`           | `string`  | [color][colors]                                                                                                                                |
| `background_templates` | `array`   | [color templates][color-templates]                                                                                                             |
| `template`             | `string`  | a go [text/template][go-text-template] template extended with [sprig][sprig] utilizing the properties below - defaults to `{{ .Shell }}> `     |
| `filler`               | `string`  | when you want to create a line with a repeated set of characters spanning the width of the terminal. Will be added _after_ the `template` text |
| `newline`              | `boolean` | add a newline before the prompt                                                                                                                |

## Enable the feature

Oh My posh handles enabling the feature automatically for all shells except `cmd` when the config contains a
transient prompt configuration. For `cmd`, you can run the command below once to enable the feature permanently:

```shell
clink set prompt.transient always
```

[ble.sh]: https://github.com/akinomyoga/ble.sh
[colors]: /docs/configuration/colors
[go-text-template]: https://golang.org/pkg/text/template/
[console-title]: /docs/configuration/title#console-title-template
[sprig]: https://masterminds.github.io/sprig/
[clink]: https://chrisant996.github.io/clink/
[templates]: /docs/configuration/templates
[color-templates]: /docs/configuration/colors#color-templates
[cstp]: /docs/configuration/templates#cross-segment-template-properties
