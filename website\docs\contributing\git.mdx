---
id: git
title: Git commands for contributors
sidebar_label: Git Commands
---

import Tabs from "@theme/Tabs";
import TabItem from "@theme/TabItem";

While we're mostly used to working with source control, working with a fork and making sure
we can merge swiftly brings some additional challenges. This page aims to help you out with the things you might
get asked to do, but which may be outside of your comfort zone.

Sit back, relax and bring your towel.

If you're not comfortable using git from the CLI, we recommend [GitKraken][kraken]. It's the best cross platform git tool and
we've added instructions on how to use it below as well.

## I didn't stick to the conventional commit guidelines

<Tabs
  defaultValue="kraken"
  groupId="git"
  values={[
    { label: 'GitKraken', value: 'kraken', },
    { label: 'CL<PERSON>', value: 'cli', },
  ]
}>
<TabItem value="kraken">

Open your oh-my-posh repo inside GitKraken and right click the commit you want to reword in the graph overview.
Select `Edit commit message`, reword it to respect the conventional commit guidelines and press `Update message`.

Click Push on the top of the screen and select `Force Push` to bring the changes to the Pull Request.

</TabItem>
<TabItem value="cli">

**I only have 1 commit**

To reword the last commit, we can make use of git's `--amend` switch to add something to our latest commit (code, changes, rewording).
Use the following commands to rephrase the last commit and get that change merged!

```bash
git commit --amend -m "feat: better worded feature"`
git push --force
```

**I added more than commit**

If all of your commits need to go to main because it makes sense to treat these as atomic units, you can use git's interactive rebase
functionality to reword any commit between `main` and your `HEAD`. To start an interactive rebase, type `git rebase -i main`.

This will open your `$EDITOR` and you can mark the commits you want to reword with `reword` (or `r`) rather than `pick`.
Exiting that file will start the rebase and spawn your `$EDITOR` to alter the commit message for each commit you marked as `reword`.

Once done, use `git push --force` to bring the changes to the pull request.

:::tip vscode
The latest version of vscode has a built-in gui to help you select `reword` or any other action on a commit. Select the right ones and press
Start Rebase to continue.
:::

</TabItem>
</Tabs>

## My branch is out of date with the remote

This means the main branch of oh-my-posh contains commits your branch does not (could be your main branch, or the branch you created to work on).
To remedy this, we need to rebase (add the new commits of oh-my-posh's main branch underneath your new commits) so the pull request can get merged.

The first thing to do is to add the oh-my-posh codebase as a remote to your local git repository. By default, your fork is a standalone copy
of oh-my-posh with its own remote on GitHub that's not connected to the oh-my-posh codebase. Forks and Pull Requests are a feature GitHub introduced
on top of git functionality, so we need to mimic that situation ourselves.


### Add the remote to your local git repository

<Tabs
  defaultValue="kraken"
  groupId="git"
  values={[
    { label: 'GitKraken', value: 'kraken', },
    { label: 'CLI', value: 'cli', },
  ]
}>
<TabItem value="kraken">

Hover over `Remote` on the left-hand side, this will show a `+` button. Click it and select GitHub. There you have the ability to select
`jandedobbeleer/oh-my-posh` and name it `upstream`. GitKraken will fetch the remote and you will see all branches underneath `upstream` as
you do for your own branches. Right click `upstream`'s `main` branch and select `Rebase <branch> onto upstream/main`.

Click Push on the top of the screen and select `Force Push` to bring the changes to the Pull Request.

</TabItem>
<TabItem value="cli">

```bash
git remote <NAME_EMAIL>:JanDeDobbeleer/oh-my-posh.git
git fetch upstream
```

</TabItem>
</Tabs>

### Rebase your branch onto upstream/main

<Tabs
  defaultValue="kraken"
  groupId="git"
  values={[
    { label: 'GitKraken', value: 'kraken', },
    { label: 'CLI', value: 'cli', },
  ]
}>
<TabItem value="kraken">

Right click `upstream`'s `main` branch and select `Rebase <branch> onto upstream/main`. Click Push on the top of the screen and select
`Force Push` to bring the changes to the Pull Request.

</TabItem>
<TabItem value="cli">

```bash
git rebase upstream/main
git push --force
```

</TabItem>
</Tabs>

[kraken]: https://www.gitkraken.com/invite/nQmDPR9D
