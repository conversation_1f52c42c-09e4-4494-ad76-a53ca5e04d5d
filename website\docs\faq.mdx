---
id: faq
title: FAQ
sidebar_label: 🤔 FAQ
---

import Tabs from "@theme/Tabs";
import TabItem from "@theme/TabItem";
import Config from "@site/src/components/Config.js";

Before validating anything, make sure you're on the [latest version][latest] of Oh My Posh and your terminal and shell are up-to-date.

### My prompt is broken after upgrading to PowerShell 7.4

PowerShell 7.4 has an [issue][pwsh-20733] in encoding which affects the rendering of Oh My Posh.
You can work around this by forcing the entire shell to UTF8.

```powershell
[Console]::OutputEncoding = [Text.Encoding]::UTF8
oh-my-posh init pwsh --config ~/custom.omp.json | Invoke-Expression
```

:::warning
Forcing the entire session to UTF8 can have unwanted side effects for other executables. We can't be held liable for that,
consider this a temporary workaround until the original PowerShell [issue][pwsh-20733] is resolved.
:::

### I want to disable the upgrade notice

You can disable the upgrade notice using the following command once:

```bash
oh-my-posh disable notice
```

If you want to enable it again, run:

```bash
oh-my-posh enable notice
```

### The prompt is slow (delay in showing the prompt between commands)

:::tip Windows
The delay you're experiencing may be linked to the real-time protection features of antivirus software.

To potentially alleviate this delay, ensure that Windows Defender or your primary antivirus software has an [exclusion][exclusion] set for the full path of the executable. To identify the full path of the executable in question, you can run the following command in a PowerShell prompt:

```powershell
(Get-Command oh-my-posh).Source
```

Additionally, check your antivirus settings for features that actively scan scripts during execution. Even with exclusions set, some antivirus programs might still actively scan scripts, which can introduce a noticeable delay.
:::

:::caution
Always proceed with caution to maintain a balance between system performance and security. Modifying security settings or adding exclusions can expose your system to potential threats.
:::

<Tabs
  defaultValue="powershell"
  groupId="shell"
  values={[
    { label: 'powershell', value: 'powershell', },
    { label: 'others', value: 'others', },
  ]
}>
<TabItem value="powershell">

You can use the `oh-my-posh debug` functionality to see where Oh My Posh spends its time.
In case there's no clear culprit (timings indicate everything's OK), chances are some modules are the culprit.
We bootstrap a few PowerShell modules to maximize compatibility, but sometimes these can introduce unwanted side-effects.

The modules we support are:

- posh-git

</TabItem>
<TabItem value="others">

You can use Oh My Posh's built-in debug functionality to identify slow segments.

```bash
oh-my-posh debug
```

Whenever there's a segment that spikes, see if there might be updates to the underlying functionality (usually shell commands).

</TabItem>
</Tabs>

If only your Git repo paths are slow, then try running [`git gc`][git-gc] to clean up and optimize the local repository.

If nothing seems to resolve the issue, feel free to [create an issue][new-issue].

### There are rectangles instead of icons in my prompt

The font you're using doesn't have the needed standard extended glyph set like [Nerd Font][nf] does.
Windows Terminal ships with Cascadia Code by default which has a powerline patched variant called Cascadia Code PL,
but also that one misses certain interesting icons. You can fall back to any theme with the `.minimal` indication,
or make use of a Nerd Font. Have a look at the [font][font] section for more context in case you're using all the right conditions.

### Text decoration styles like bold and dimmed don't work

The text decoration styles are based on your terminal emulator's capabilities.

A quick way to check if your terminal supports a specific style escape sequence is to take a look at the `terminfo` database, on Linux.
Refer [this page on ArchWiki][arch-terminfo].

If you are on Windows, use Windows Terminal. It closely copies the `xterm-256color` capabilities.
See the [PowerShell docs on terminal support][ps-ansi-docs] and [this GitHub comment][xterm-gh-comment].

### Windows Terminal: Unexpected space between segments/text

Windows Terminal has some issues with [rendering certain glyphs][wt-glyph]. These issues are on [their backlog][wt-glyphs].
A temporary workaround is to use an invisible character at the end (`\u2800`), or a zero width character (`\u200a`) before the icon.

In case you didn't export the config yet (it's the default, or you're using the `--config` flag with a predefined theme), you can follow
the steps [here][export] to export it to a local file so you can adjust the [segment's][segment] `template`.

In the example below, it's assumed that the execution segment's icon `\ueba2` has an unexpected space after it in Windows Terminal.

<Config
  data={{
    type: "executiontime",
    template: "\ueba2", // unexpected space
  }}
/>

Adjust it by adding `\u2800` immediately after the icon.

<Config
  data={{
    type: "executiontime",
    template: "\ueba2\u2800", // solved
  }}
/>

### Jetbrains terminals: Icons do not render

They need to work on their terminal, somehow it only supports UTF-8 and not UTF-16.
[An issue][jb-icons] is available for a follow-up here.

### Strange colouring after exiting VIM or when using the PowerShell progress bootstrap

This bug is caused by Windows Terminal and/or VIM. There are two issues for this, one at [Windows Terminal][wt-vim] and
one at [VIM][vim-wt].

### Conda: environment name displayed in front of the prompt

Conda will automatically prepend the prompt with the name of the environment you're in.
To solely rely on Oh My Posh to set the prompt, you can configure the following setting to hide it.
Make sure to add this **before** initializing Oh My Posh.

```bash
conda config --set changeps1 False
```

### Python venv: Prompt changes on activating virtual environment

Virtual environments created with `venv` add the active environment's name to the prompt automatically.
To disable this behaviour, set `VIRTUAL_ENV_DISABLE_PROMPT` environment variable to `1` on your system. Example files:

Note: Tilde (~) in paths refers to your user's home directory.

<Tabs
  defaultValue="powershell"
  groupId="shell"
  values={[
    { label: 'powershell', value: 'powershell', },
    { label: 'fish', value: 'fish', },
    { label: 'others', value: 'others', },
  ]
}>
<TabItem value="powershell">

```powershell
# Your PowerShell $PROFILE
$env:VIRTUAL_ENV_DISABLE_PROMPT=1
```

</TabItem>
<TabItem value="fish">

```fish
# ~/.config/fish/config.fish
set -x VIRTUAL_ENV_DISABLE_PROMPT 1
```

</TabItem>
<TabItem value="others">

```bash
# ~/.bashrc (bash) or ~/.zprofile or ~/.zshrc (zsh)
export VIRTUAL_ENV_DISABLE_PROMPT=1
```

</TabItem>
</Tabs>

### PowerShell: The term 'Set-Theme' is not recognized as the name of a cmdlet, function, script file, or operable program.

You need to migrate using the following [guide][migrating].

### PowerShell: Running in ConstrainedLanguage mode

You're here because you've seen the following message:

```powershell
[WARNING] ConstrainedLanguage mode detected, unable to set console to UTF-8.
When using PowerShell in ConstrainedLanguage mode, please set the
console mode manually to UTF-8. See here for more information:
https://ohmyposh.dev/docs/faq#powershell-running-in-constrainedlanguage-mode
```

When running PowerShell in ConstrainedLanguage mode, we can't set the console to UTF-8. This will cause the prompt to be rendered incorrectly.
There's a few [options][utf-8] to set the console to UTF-8 from an OS perspective on Windows, other systems shouldn't be impacted.

To remove the message after manual configuration, you can add the following to your `$PROFILE` before importing Oh My Posh:

```powershell
$env:POSH_CONSTRAINED_LANGUAGE = 1
```

### PowerShell: The term '.../oh-my-posh.exe' is not recognized as a name of a cmdlet...

For example:

```
&: The term 'C:/Users/<USER>/.oh-my-posh/oh-my-posh.exe' is not recognized as a name of a cmdlet, function, script file, or executable program.
Check the spelling of the name, or if a path was included, verify that the path is correct and try again.
```

The issue is that PowerShell on Windows doesn't yet default to UTF-8. Resolve the issue by setting the shell to UTF-8 in the scope of initializing Oh My Posh.

:::info Why it matters?
If the location contains non-ASCII characters, non-UTF-8 PowerShell may provide a wrong path to Oh My Posh, which can break the initialization.

The scenario for non-ASCII location:

- Your computer has a non-ASCII username.
- Your [config file](./installation/customize.mdx) is under your `$HOME`.
  :::

To do so, edit your `$PROFILE`, find the line that initializes Oh My Posh (as highlighted below), and change it to the following:

```powershell
$previousOutputEncoding = [Console]::OutputEncoding
[Console]::OutputEncoding = [Text.Encoding]::UTF8
try {
    // highlight-start
    oh-my-posh init pwsh --config ~/custom.omp.json | Invoke-Expression
    // highlight-end
} finally {
    [Console]::OutputEncoding = $previousOutputEncoding
}
```

Alternatively, let the whole shell become UTF-8. (Be aware: Unwanted side effects can happen.) Add the following line to the top of your `$PROFILE`:

```powershell
$OutputEncoding = [console]::InputEncoding = [console]::OutputEncoding = New-Object System.Text.UTF8Encoding
```

### Zsh: No command history (Ctrl+R does not work)

This issue occurs when you're using plain zsh in combination with Oh My Posh.
You can fix this by adding the right configuration to `~/.zshrc`.

```bash
HISTFILE=~/.zsh_history
HISTSIZE=10000
SAVEHIST=10000
setopt appendhistory
```

### ZSH: No such shell function 'zle-line-init'

This is most likely caused by two Oh My Posh init lines in your `.zshrc`, remove one of them. See [here][zsh-init].

### Fish: Display current bind (Vim) mode

By default, Oh My Posh will not re-render the prompt (i.e., generate a new prompt) until a new command is run, so you should
export `$fish_bind_mode` to `FISH__BIND_MODE` and call the `omp_repaint_prompt` function to do prompt re-rendering when
`$fish_bind_mode` changes:

```fish
function rerender_on_bind_mode_change --on-variable fish_bind_mode
    if test "$fish_bind_mode" != paste -a "$fish_bind_mode" != "$FISH__BIND_MODE"
        set -gx FISH__BIND_MODE $fish_bind_mode
        omp_repaint_prompt
    end
end
```

Next, mask the `fish_default_mode_prompt` function to prevent it from echoing the current mode:

```fish
function fish_default_mode_prompt --description "Display vi prompt mode"
    # This function is masked and does nothing
end
```

After that, you can use the value in a template. The following replicates the [example in the Fish documentation][fish-mode-prompt]:

<Config
  data={{
    type: "text",
    template:
      '{{ if eq .Env.FISH__BIND_MODE "default" }}<red>[N]</>{{ else if eq .Env.FISH__BIND_MODE "insert" }}<green>[I]</>{{ else if eq .Env.FISH__BIND_MODE "replace_one" }}<green>[R]</>{{ else if eq .Env.FISH__BIND_MODE "visual"}}<brmagenta>[V]</>{{ else }}<red>[?]</>{{ end }}',
  }}
/>

### Fish: Integrate with `Alt+←` and `Alt+→` Bindings to `prevd` and `nextd`

Fish supports using `Alt+←` (Alt+Left) and `Alt+→` (Alt+Right) bindings to [navigate the directory history](https://fishshell.com/docs/current/interactive.html#id13).

To have oh-my-posh update the directory context as the directory history is navigated, use a function that calls `omp_repaint_prompt` when the `$PWD` variable changes:

```fish
function rerender_on_dir_change --on-variable PWD
  omp_repaint_prompt
end
```

### After updating my Nerd Font to a newer version, the prompt displays unknown characters

Nerd Fonts moved the icons to a different location in the font for v3.
This can cause the prompt to display unknown characters. There's a built-in migration in Oh My Posh to fix this.

To migrate, run the following command:

```bash
oh-my-posh config migrate glyphs --write
```

This will update your configuration file to use the new glyph locations. Do know they might look different, as they also
updated the icons themselves. A backup of the current config can be found in the same location with a `.bak` extension.

### Xonsh: Right prompt jumps to bottom of the screen

This is a known problem with Xonsh. The issue is tracked [here][xonsh-issue].

[pwsh-20733]: https://github.com/PowerShell/PowerShell/issues/20733
[exclusion]: https://support.microsoft.com/en-us/windows/add-an-exclusion-to-windows-security-811816c0-4dfd-af4a-47e4-c301afe13b26
[arch-terminfo]: https://wiki.archlinux.org/title/Bash/Prompt_customization#Terminfo_escape_sequences
[ps-ansi-docs]: https://docs.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_ansi_terminals?view=powershell-7.2
[xterm-gh-comment]: https://github.com/microsoft/terminal/issues/6045#issuecomment-631743728
[git-gc]: https://git-scm.com/docs/git-gc
[new-issue]: https://github.com/JanDeDobbeleer/oh-my-posh/issues/new/choose
[latest]: https://github.com/JanDeDobbeleer/oh-my-posh/releases/latest
[wt-glyph]: https://github.com/microsoft/terminal/issues/3546
[wt-glyphs]: https://github.com/microsoft/terminal/issues?q=is%3Aissue+is%3Aopen+unicode+width
[export]: installation/customize.mdx#custom-configuration
[segment]: configuration/segment.mdx
[nf]: https://www.nerdfonts.com/
[font]: /docs/installation/fonts
[jb-icons]: https://youtrack.jetbrains.com/issue/IDEA-248010
[migrating]: /docs/migrating
[wt-vim]: https://github.com/microsoft/terminal/issues/3794
[vim-wt]: https://github.com/vim/vim/issues/5092
[utf-8]: https://github.com/PowerShell/PowerShell/issues/7233#issuecomment-640243647
[fish-changelog]: https://fishshell.com/docs/current/relnotes.html#id1
[xonsh-issue]: https://github.com/xonsh/xonsh/issues/3810
[zsh-init]: https://github.com/JanDeDobbeleer/oh-my-posh/discussions/3462#discussioncomment-5155790
[fish-mode-prompt]: https://fishshell.com/docs/current/cmds/fish_mode_prompt.html#example
