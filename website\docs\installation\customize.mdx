---
id: customize
title: Customize
sidebar_label: 🧩 Customize
---

import Tabs from "@theme/Tabs";
import TabItem from "@theme/TabItem";

The standard initialization sets Oh My Posh' default, built-in theme.

To set a new configugration or theme you need to change the `--config` option of the `oh-my-posh init <shell>`
line in your `profile` or `.<shell>rc` script (see [prompt][prompt]) and point it to the location of a
predefined [theme][themes] or custom configuration.

There are two possible values the `--config` flag can handle:

- a path to a local configuration file

  ```powershell
  --config 'C:/Users/<USER>/jandedobbeleer.omp.json'
  ```

- a URL pointing to a remote config

  ```powershell
  --config 'https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/jandedobbeleer.omp.json'
  ```

### Set the configuration

The example below use a local path to the [jandedobbeleer][jandedobbeleer] theme, adjust the `--config` value
to reflect your configuration file, local or remote.

<Tabs
  defaultValue="powershell"
  groupId="shell"
  values={[
    { label: 'bash', value: 'bash', },
    { label: 'cmd', value: 'cmd', },
    { label: 'elvish', value: 'elvish', },
    { label: 'fish', value: 'fish', },
    { label: 'nu', value: 'nu', },
    { label: 'powershell', value: 'powershell', },
    { label: 'xonsh', value: 'xonsh', },
    { label: 'zsh', value: 'zsh', },
  ]
}>
<TabItem value="bash">

:::caution Git bash
Use the full path to the config file, not the relative path or `~` as a shorthand for `$HOME`.
:::

Adjust the Oh My Posh init line in `~/.bashrc` (could be `~/.profile` or `~/.bash_profile` depending
on your environment) by adding the `--config` flag with the location of your configuration.

```bash
eval "$(oh-my-posh init bash --config ~/jandedobbeleer.omp.json)"
```

Once altered, reload your profile for the changes to take effect.

```bash
exec bash
```

</TabItem>
<TabItem value="cmd">

Adjust the Oh My Posh init line in `oh-my-posh.lua` by adding the `--config` flag with the location
of your configuration.

```lua title="oh-my-posh.lua"
load(io.popen('oh-my-posh init cmd --config C:/Users/<USER>/jandedobbeleer.omp.json'):read("*a"))()
```

:::caution
Use the full path to the config file, not the relative path. You can make use of **forward slashes** to
avoid having to use double backslashes.
:::

Once altered, restart cmd for the changes to take effect.

</TabItem>
<TabItem value="elvish">

Adjust the Oh My Posh init line in `~/.elvish/rc.elv` by adding the `--config` flag with the location
of your configuration.

```bash
eval (oh-my-posh init elvish --config ~/jandedobbeleer.omp.json)
```

Once added, reload your profile for the changes to take effect.

```bash
exec elvish
```

</TabItem>
<TabItem value="fish">

:::caution
It is recommended to use the latest version of Fish. Versions below 3.1.2 have issues displaying the prompt.
:::

Adjust the Oh My Posh init line in `~/.config/fish/config.fish` by adding the `--config` flag with
the location of your configuration.

```bash
oh-my-posh init fish --config ~/jandedobbeleer.omp.json | source
```

Once altered, reload your config for the changes to take effect.

```bash
. ~/.config/fish/config.fish
```

</TabItem>
<TabItem value="nu">

:::caution
Oh My Posh requires Nushell `v0.104.0` or higher.
:::

Adjust the Oh My Posh init line in the Nushell config file (`$nu.config-path`) by adding the `--config` flag
with the location of your configuration.

```bash
oh-my-posh init nu --config ~/jandedobbeleer.omp.json
```

</TabItem>
<TabItem value="powershell">

Adjust the Oh My Posh init line in your `$PROFILE` by adding the `--config` flag with the location
of your configuration.

```powershell
oh-my-posh init pwsh --config ~/jandedobbeleer.omp.json | Invoke-Expression
```

Once altered, reload your profile for the changes to take effect.

```powershell
. $PROFILE
```

:::info
When the above command gives an error, make sure to create the profile first and add the `oh-my-posh init` above.

```powershell
New-Item -Path $PROFILE -Type File -Force
```

In this scenario, it can also be that PowerShell blocks running local scripts. To solve that, set PowerShell
to only require remote scripts to be signed using `Set-ExecutionPolicy RemoteSigned`, or [sign the profile][sign].
:::

</TabItem>
<TabItem value="xonsh">

Adjust the Oh My Posh init line in `~/.xonshrc` by adding the `--config` flag with the location of your configuration.

```bash
execx($(oh-my-posh init xonsh --config ~/jandedobbeleer.omp.json))
```

Once added, reload your profile for the changes to take effect.

```bash
exec xonsh
```

</TabItem>
<TabItem value="zsh">

Adjust the Oh My Posh init line in `~/.zshrc` by adding the `--config` flag with the location of your configuration.

```bash
eval "$(oh-my-posh init zsh --config ~/jandedobbeleer.omp.json)"
```

Once altered, reload your profile for the changes to take effect.

```bash
exec zsh
```

</TabItem>
</Tabs>

:::tip wsl
When using oh-my-posh in Windows and the WSL, know that you can **share your theme with the WSL** by pointing to a theme in your
Windows user's home folder.

Inside the WSL, you can find your Windows user's home folder here: `/mnt/c/Users/<USER>
:::

### Themes

The Windows and homebrew installers also bundle the **predefined configurations** ([themes][themes]). You can use the
following way to reference them directly. This will keep them up-to-date and compatible with future updates.

<Tabs
  defaultValue="windows"
  groupId="shell"
  values={[
    { label: 'windows', value: 'windows', },
    { label: 'homebrew', value: 'homebrew', },
  ]
}>
<TabItem value="windows">

For example, to use the [jandedobbeleer][jandedobbeleer] theme, alter the init line like this (`powershell`):

```powershell
oh-my-posh init pwsh --config "$env:POSH_THEMES_PATH/jandedobbeleer.omp.json" | Invoke-Expression
```

</TabItem>
<TabItem value="homebrew">

When using homebrew, all themes are installed alongside Oh My Posh in `$(brew --prefix oh-my-posh)/themes`.
To use any of the themes, use the following syntax (`zsh`):

```bash
eval "$(oh-my-posh init zsh --config $(brew --prefix oh-my-posh)/themes/jandedobbeleer.omp.json)"
```

</TabItem>
</Tabs>

### Custom configuration

Maybe there's a theme you like, but you don't fancy the colors. Or, maybe there's a segment you
want to tweak/add, or replace some of the icons with a different one. Whatever the case, **read through
available options first**, by starting with the [configuration][configuration].

You can export the current theme (default, or set via `--config`) to the format you like (`json`, `yaml`, or `toml`)
which can be used to tweak and store as your own custom configuration.

```bash
oh-my-posh config export --output ~/.mytheme.omp.json
```

:::caution
Do not adjust the theme files in their original location as they're updated together with Oh My Posh
which will remove your customizations. Always copy, or export them and save the new configuration outside of the
Oh My Posh internal themes folder.
:::

#### Previewing changes

If you want to preview your changes, you can use the following command to render every configured prompt.

```bash
oh-my-posh print preview
```

Use the `--force` flag in case you want to render all segments, regardless of the current context.

```bash
oh-my-posh print preview --force
```

### Read the docs

To fully understand how to customize a theme, read through the documentation in the configuration and segments sections.
The [configuration][configuration] section covers the basic building blocks and concepts of Oh My Posh themes, while the
segments section covers how to configure each available segment.

[themes]: themes.md
[configuration]: configuration/general.mdx
[prompt]: prompt.mdx
[jandedobbeleer]: /docs/themes#jandedobbeleer
[json-schema]: https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json
[homebrew-problem]: https://github.com/JanDeDobbeleer/oh-my-posh/discussions/2644
[sign]: https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_signing?view=powershell-7.3#methods-of-signing-scripts
