---
id: fonts
title: Fonts
sidebar_label: 🔤 Fonts
---

import Tabs from "@theme/Tabs";
import TabItem from "@theme/TabItem";

### Nerd Fonts

Oh My Posh was designed to use [Nerd Fonts][nerdfonts]. Nerd Fonts are popular fonts that are patched to include icons.
To see the icons displayed in Oh My Posh, **install** a [Nerd Font][nerdfonts], and **configure** your terminal to use it.

:::info
Fonts need to be installed on the host system as this is a UI setting, not a command line, or shell configuration.
This means **when running inside a container or WSL**, you need to install the font on the host system and configure
your terminal emulator to use it.
:::

### Installation

<Tabs
    defaultValue="oh-my-posh"
    values={[
        { label: 'Oh My Posh', value: 'oh-my-posh' },
        { label: 'homebrew', value: 'homebrew' }
    ]
}>
<TabItem value="oh-my-posh">

Oh My Posh has a CLI to help you select and install a [Nerd Font][nerdfonts]:

:::info
When running as root/administrator, the fonts will be installed system-wide.
When running as a regular user, the fonts will be installed in the user's font directory.
By default, Oh My Posh installs the `.ttf` version of the font in case multiple versions are available.
:::

```bash
oh-my-posh font install
```

This will present a list of Nerd Font libraries, from which you can  select `Meslo`, which includes the `Meslo LGM NF` font we recommend. Note that you can also  install it directly via:

```bash
oh-my-posh font install meslo
```

</TabItem>
<TabItem value="homebrew">

If you're using Homebrew, you can search for and install Nerd Fonts directly:

```bash
brew search nerd-font

brew install --cask font-fira-code-nerd-font
```

</TabItem>
</Tabs>

### Configuration

Make sure to **configure your terminal** to use the font you have installed. The following sections will show you how to do this for the most popular terminals.

<Tabs
    defaultValue="wt"
    values={[
        { label: 'Windows Terminal', value: 'wt' },
        { label: 'Visual Studio Code', value: 'code' },
        { label: 'Visual Studio', value: 'vs' }
    ]
}>
<TabItem value="wt">

Once you have installed a Nerd Font, you will need to configure the Windows Terminal to use it. This can be easily done
by modifying the Windows Terminal settings (default shortcut: `CTRL + SHIFT + ,`). In your `settings.json` file, add the
`font.face` attribute under the `defaults` attribute in `profiles`:

```json
{
    "profiles":
    {
        "defaults":
        {
            "font":
            {
                "face": "MesloLGM Nerd Font"
            }
        }
    }
}
```

</TabItem>
<TabItem value="code">

When using Visual Studio Code, you will need to configure the integrated Terminal to make use of the Nerd Font as well. This can be done by changing the `Integrated: Font Family` value in the Terminal settings (default shortcut: `CTRL + ,` and search for `Integrated: Font Family` or via `Users` -> `Features` -> `Terminal`).

If you are using the JSON based settings, you will need to update the `terminal.integrated.fontFamily` value. Example in case of `MesloLGM Nerd Font` Nerd Font:

```json
"terminal.integrated.fontFamily": "MesloLGM Nerd Font"
```

</TabItem>
<TabItem value="vs">

When using Visual Studio, you will need to configure the integrated Terminal to make use of the Nerd Font as well. This can be done by opening the settings
in `Tools > Options > Fonts and Colors > Terminal` and selecting a font like `MesloLGM Nerd Font`.

:::warning
OTF fonts do not appear in Visual Studio's Terminal settings, only TTF fonts. See [here][vs-otf] for more information.
:::

</TabItem>
</Tabs>

### Other Fonts

If you are not interested in using a Nerd Font, you will want to use a theme which doesn't include any Nerd Font icons.
The `minimal` themes do not make use of Nerd Font icons.

[Creating your own theme][configuration] is always an option too 😊

[nerdfonts]: https://www.nerdfonts.com/
[configuration]: /docs/installation/customize
[wt-issue-8993]: https://github.com/microsoft/terminal/issues/8993
[vs-otf]: https://stackoverflow.com/questions/75252606/is-it-possible-to-use-nerd-fonts-as-the-font-for-terminals-in-visual-studio
