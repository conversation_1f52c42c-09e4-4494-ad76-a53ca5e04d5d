A [Homebrew][brew] Formula and Cask (macOS only) are available for easy installation.

```bash
brew install jandedobbeleer/oh-my-posh/oh-my-posh
```

This installs two things:

- `oh-my-posh` - Executable, added to `$(brew --prefix)/bin`
- `themes` - The latest Oh My Posh [themes][themes]

If you want to use a predefined theme, you can find them in `$(brew --prefix oh-my-posh)/themes`, referencing them as such
will always keep them compatible when updating Oh My Posh.

## Updating

```bash
brew update && brew upgrade oh-my-posh
```

:::tip
In case you see [strange behaviour][strange] in your shell, reload it after upgrading Oh My Posh.
For example in zsh:

```bash
brew update && brew upgrade && exec zsh
```
:::

[brew]: https://brew.sh
[themes]: https://ohmyposh.dev/docs/themes
[strange]: https://github.com/JanDeDobbeleer/oh-my-posh/issues/1287
