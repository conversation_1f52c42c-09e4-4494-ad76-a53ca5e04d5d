---
id: linux
title: Linux
sidebar_label: 🐧 Linux
---

import Tabs from "@theme/Tabs";
import TabItem from "@theme/TabItem";
import InstallHomebrew from "./homebrew.mdx";
import Next from "./next.mdx";

## Set up your terminal

Oh My Posh uses ANSI color codes under the hood, these should work in every terminal,
but you may have to set the environment variable `$TERM` to `xterm-256color` for it to work.

:::info
To display all icons, we recommend the use of a [Nerd Font][fonts].
:::

## Installation

<Tabs
  defaultValue="homebrew"
  groupId="install"
  values={[
    { label: 'manual', value: 'manual', },
    { label: 'homebrew', value: 'homebrew', }
  ]
}>
<TabItem value="manual">

:::info
Before running the below commands, make sure that you have updated `curl` and the
client certificate store on the target machine.
:::

Install the latest version for your system by running the following command:

```bash
curl -s https://ohmyposh.dev/install.sh | bash -s
```

By default the script will install to `~/bin` or `~/.local/bin` depending on which one exists,
or the existing Oh My Posh executable's installation folder.
If you want to install to a different location you can specify it using the `-d` flag:

```bash
curl -s https://ohmyposh.dev/install.sh | bash -s -- -d ~/bin
```

</TabItem>
<TabItem value="homebrew">

:::warning
When installing Homebrew for Linux,
be sure to follow *[Next steps][nextsteps]* instructions to add Homebrew to your PATH and to your
bash shell profile script, and *[Requirements][requirements]* to build Oh My Posh.
:::

<InstallHomebrew />

</TabItem>
</Tabs>

<Next />

[requirements]: https://docs.brew.sh/Homebrew-on-Linux#requirements
[nextsteps]: https://docs.brew.sh/Homebrew-on-Linux#install
[fonts]: /docs/installation/fonts
