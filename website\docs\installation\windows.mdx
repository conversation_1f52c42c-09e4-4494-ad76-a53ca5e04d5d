---
id: windows
title: Windows
sidebar_label:  🪟  Windows
---

import ThemedImage from '@theme/ThemedImage';
import Tabs from "@theme/Tabs";
import TabItem from "@theme/TabItem";
import Next from "./next.mdx";

## Set up your terminal

While Oh My Posh works on the standard terminal, we advise using the [Windows Terminal][wt].

:::info
To display all icons, we recommend the use of a [Nerd Font][fonts].
:::

:::caution
When using oh-my-posh inside the WSL, make sure to follow the [Linux][linux] installation guide.
:::

<a href="https://apps.microsoft.com/detail/xp8k0hkjfrxgck?mode=mini" target="_blank">
  <ThemedImage
    alt="msstore"
    width="232"
    height="68"
    sources={{
      light: '/img/msstore-dark.svg',
      dark: '/img/msstore-light.svg',
    }}
  />
</a>

## Installation

<Tabs
  defaultValue="winget"
  groupId="install"
  values={[
    { label: 'winget', value: 'winget', },
    { label: 'manual', value: 'manual', },
    { label: 'chocolatey', value: 'chocolatey'},
  ]
}>
<TabItem value="winget">

Open a PowerShell prompt and run the following command:

```powershell
winget install JanDeDobbeleer.OhMyPosh -s winget
```

</TabItem>
<TabItem value="manual">

Open a PowerShell prompt and run the following command:

```powershell
Set-ExecutionPolicy Bypass -Scope Process -Force; Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://ohmyposh.dev/install.ps1'))
```

</TabItem>
<TabItem value="chocolatey">

:::info
The chocolatey package is maintained by the community and might not be up to date.
In case of issues, please contact the [maintainer][choco-maintainer].
:::

Open a PowerShell prompt and run the following command:

```powershell
choco install oh-my-posh
```

</TabItem>
</Tabs>

This installs a couple of things:

- `oh-my-posh.exe` - Windows executable
- `themes` - The latest Oh My Posh [themes][themes]

:::info
For the `PATH` to be reloaded, a restart of your terminal is advised.
If oh-my-posh is not recognized as a command, you can run the installer again, or add it manually to your `PATH`.
For example:

```powershell
$env:Path += ";C:\Users\<USER>\AppData\Local\Programs\oh-my-posh\bin"
```
:::

:::tip Antivirus software
Due to frequent updates of Oh My Posh, Antivirus software occasionally flags it (false positive).
To ensure Oh My Posh isn't blocked you can either report it to your favorite Antivirus software as false positive
(e.g. [Report a false positive/negative to Microsoft for analysis][report-false-positive]) or create an exclusion for it.
Exclusions should be added with the full path to the executable, you can get it with the following command from a PowerShell prompt:

```powershell
(Get-Command oh-my-posh).Source
```
:::

<Next />

## Update

<Tabs
  defaultValue="winget"
  groupId="install"
  values={[
    { label: 'winget', value: 'winget', },
    { label: 'manual', value: 'manual', },
    { label: 'chocolatey', value: 'chocolatey'},
  ]
}>
<TabItem value="winget">

Open a PowerShell prompt and run the following command:

```powershell
winget upgrade JanDeDobbeleer.OhMyPosh -s winget
```

</TabItem>
<TabItem value="manual">

Open a PowerShell prompt and run the following command:

```powershell
Set-ExecutionPolicy Bypass -Scope Process -Force; Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://ohmyposh.dev/install.ps1'))
```

</TabItem>
<TabItem value="chocolatey">

:::info
The chocolatey package is maintained by the community and might not be up to date.
In case of issues, please contact the [maintainer][choco-maintainer].
:::

Open a PowerShell prompt and run the following command:

```powershell
choco upgrade oh-my-posh
```

</TabItem>
</Tabs>

## Default themes

You can find the themes in the folder indicated by the environment variable `POSH_THEMES_PATH`.
For example, you can use `oh-my-posh init pwsh --config "$env:POSH_THEMES_PATH\jandedobbeleer.omp.json" | Invoke-Expression`
for the prompt initialization in PowerShell.


[fonts]: /docs/installation/fonts
[wt]: https://github.com/microsoft/terminal
[linux]: /docs/installation/linux
[themes]: /docs/themes
[report-false-positive]: https://docs.microsoft.com/en-us/microsoft-365/security/defender/m365d-autoir-report-false-positives-negatives#report-a-false-positivenegative-to-microsoft-for-analysis
[choco-maintainer]: https://github.com/digitalcoyote/chocolatey-packages
