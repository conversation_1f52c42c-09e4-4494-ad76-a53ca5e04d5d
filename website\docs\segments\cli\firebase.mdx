---
id: firebase
title: Firebase
sidebar_label: Firebase
---

## What

Display the current active Firebase project.

This segment leverages the `firebase-tools.json` file generated by the [Firebase CLI][firebase-cli-docs].
The active files listed there are used to determine the current project against the current directory.

## Sample Configuration

import Config from '@site/src/components/Config.js';

<Config data={{
  "type": "firebase",
  "style": "powerline",
  "powerline_symbol": "\uE0B0",
  "foreground": "#ffffff",
  "background": "#FFA000",
  "template": " \udb82\udd67 {{ .Project }}"
}}/>

## Template ([info][templates])

:::note default template

```template
{{ .Project }}
```

:::

### Properties

| Name       | Type     | Description                                                              |
| ---------- | -------- | ------------------------------------------------------------------------ |
| `.Project` | `string` | the currently active project                                             |

[templates]: /docs/configuration/templates
[firebase-cli-docs]: https://firebase.google.com/docs/cli
