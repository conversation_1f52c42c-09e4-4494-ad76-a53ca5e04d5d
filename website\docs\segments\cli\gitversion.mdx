---
id: gitversion
title: GitVersion
sidebar_label: GitVersion
---

## What

Display the [GitVersion][gitversion] version.
We _strongly_ recommend using [GitVersion Portable][gitversion-portable] for this.

:::caution
The GitVersion CLI can be a bit slow, causing the prompt to feel slow. This is why we cache
the value for 30 minutes by default.
:::

## Sample Configuration

import Config from '@site/src/components/Config.js';

<Config data={{
  "type": "gitversion",
  "style": "powerline",
  "powerline_symbol": "\uE0B0",
  "foreground": "#ffffff",
  "background": "#3a579b",
  "template": " \uF1D2 {{ .MajorMinorPatch }} "
}}/>

## Template ([info][templates])

:::note default template

```template
{{ .MajorMinorPatch }}
```

:::

### Properties

You can leverage all variables from the [GitVersion][gitversion] CLI. Have a look at their [documentation][docs] for more information.

[gitversion]: https://github.com/GitTools/GitVersion
[gitversion-portable]: http://chocolatey.org/packages/GitVersion.Portable
[templates]: /docs/configuration/templates
[docs]: https://gitversion.net/docs/reference/variables
