---
id: helm
title: He<PERSON>
sidebar_label: Helm
---

## What

Display the version of helm

## Sample Configuration

import Config from '@site/src/components/Config.js';

<Config data={{
  "background": "#a7cae1",
  "foreground": "#100e23",
  "powerline_symbol": "\ue0b0",
  "template": " Helm {{ .Version }}",
  "style": "powerline",
  "type": "helm"
}}/>

## Properties

| Name           | Type     | Default  | Description                                                                                                                                                      |
| -------------- | :------: | :------: | ---------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `display_mode` | `string` | `always` | <ul><li>`always`: the segment is always displayed</li><li>`files`: the segment is only displayed when a chart source file `Chart.yaml` (or `Chart.yml`) or helmfile `helmfile.yaml` (or `helmfile.yml`) is present </li></ul> |

## Template ([info][templates])

:::note default template

```template
 Helm {{ .Version }}
```

:::

### Properties

| Name         | Type     | Description                |
| ------------ | -------- | -------------------------- |
| `.Version`   | `string` | Helm cli version           |

[templates]: /docs/configuration/templates
