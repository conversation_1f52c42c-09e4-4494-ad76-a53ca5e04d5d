---
id: kubectl
title: Kubernetes
sidebar_label: Kubernetes
---

## What

Display the currently active Kubernetes context name and namespace name.

## Sample Configuration

import Config from "@site/src/components/Config.js";

<Config
  data={{
    type: "kubectl",
    style: "powerline",
    powerline_symbol: "\uE0B0",
    foreground: "#000000",
    background: "#ebcc34",
    template: " \uFD31 {{.Context}}{{if .Namespace}} :: {{.Namespace}}{{end}} ",
    properties: {
      context_aliases: {
        "arn:aws:eks:eu-west-1:1234567890:cluster/posh": "posh",
      },
    },
  }}
/>

## Properties

| Name               |   Type    | Default | Description                                                                     |
| ------------------ | :-------: | :-----: | ------------------------------------------------------------------------------- |
| `display_error`    | `boolean` | `false` | show the error context when failing to retrieve the kubectl information         |
| `parse_kubeconfig` | `boolean` | `true`  | parse kubeconfig files instead of calling out to kubectl to improve performance |
| `context_aliases`  | `object`  |         | custom context namespace                                                        |

## Template ([info][templates])

:::note default template

```template
{{ .Context }}{{ if .Namespace }} :: {{ .Namespace }}{{ end }}
```

:::

### Properties

| Name         | Type     | Description                           |
| ------------ | -------- | ------------------------------------- |
| `.Context`   | `string` | the current kubectl context           |
| `.Namespace` | `string` | the current kubectl context namespace |
| `.User`      | `string` | the current kubectl context user      |
| `.Cluster`   | `string` | the current kubectl context cluster   |

:::tip

It is common for the Kubernetes "default" namespace to be used when no namespace is provided. If you want your prompt to
render an empty current namespace using the word "default", you can use something like this for the template:

```
{{.Context}} :: {{if .Namespace}}{{.Namespace}}{{else}}default{{end}}
```

:::

[templates]: /docs/configuration/templates
