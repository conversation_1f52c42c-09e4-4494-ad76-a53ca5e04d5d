---
id: quasar
title: Quasar
sidebar_label: Quasar
---

## What

Display the currently active [Quasar CLI][quasar-cli] version. Only rendered when the current or
parent folder contains a `quasar.config` or `quasar.config.js` file.

## Sample Configuration

import Config from "@site/src/components/Config.js";

<Config
  data={{
    type: "quasar",
    style: "powerline",
    powerline_symbol: "\uE0B0",
    foreground: "#00B4FF",
    template:
      " \uea6a {{.Full}}{{ if .HasVite }} \ueb29 {{ .Vite.Version }}{{ end }} ",
  }}
/>

## Properties

| Name                   |    Type    |              Default              | Description                                                                                                                                                                                                                          |
| ---------------------- | :--------: | :-------------------------------: | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `home_enabled`         | `boolean`  |              `false`              | display the segment in the HOME folder or not                                                                                                                                                                                        |
| `missing_command_text` |  `string`  |                                   | text to display when the command is missing                                                                                                                                                                                          |
| `fetch_version`        | `boolean`  |              `true`               | fetch the NPM version                                                                                                                                                                                                                |
| `cache_duration`       |  `string`  |               `24h`               | the duration for which the version will be cached. The duration is a string in the format `1h2m3s` and is parsed using the [time.ParseDuration] function from the Go standard library. To disable the cache, use `none`              |
| `display_mode`         |  `string`  |             `context`             | <ul><li>`always`: the segment is always displayed</li><li>`files`: the segment is only displayed when file `extensions` listed are present</li><li>`context`: displays the segment when the environment or files is active</li></ul> |
| `version_url_template` |  `string`  |                                   | a go [text/template][go-text-template] [template][templates] that creates the URL of the version info / release notes                                                                                                                |
| `fetch_dependencies`   | `boolean`  |              `false`              | fetch the version number of the `vite` and `@quasar/app-vite` dependencies if present                                                                                                                                                |
| `extensions`           | `[]string` | `quasar.config, quasar.config.js` | allows to override the default list of file extensions to validate                                                                                                                                                                   |
| `folders`              | `[]string` |                                   | allows to override the list of folder names to validate                                                                                                                                                                              |

## Template ([info][templates])

:::note default template

```template
\uea6a {{.Full}}{{ if .HasVite }} \ueb29 {{ .Vite.Version }}{{ end }}
```

:::

### Properties

| Name       | Type         | Description                                        |
| ---------- | ------------ | -------------------------------------------------- |
| `.Full`    | `string`     | the full version                                   |
| `.Major`   | `string`     | major number                                       |
| `.Minor`   | `string`     | minor number                                       |
| `.Patch`   | `string`     | patch number                                       |
| `.URL`     | `string`     | URL of the version info / release notes            |
| `.Error`   | `string`     | error encountered when fetching the version string |
| `.Vite`    | `Dependency` | the `vite` dependency, if found                    |
| `.AppVite` | `Dependency` | the `@quasar/app-vite` dependency, if found        |

#### Dependency

| Name       | Type      | Description                   |
| ---------- | --------- | ----------------------------- |
| `.Version` | `string`  | the full version              |
| `.Dev`     | `boolean` | development dependency or not |

[go-text-template]: https://golang.org/pkg/text/template/
[templates]: /docs/configuration/templates
[quasar-cli]: https://quasar.dev/start/quasar-cli
[time.ParseDuration]: https://golang.org/pkg/time/#ParseDuration
