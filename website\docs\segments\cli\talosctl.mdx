---
id: talosctl
title: Talosctl Context
sidebar_label: Talosctl
---

## What

Displays the currently active Talosctl context name.

This segment leverages the `.talos/config` file generated by the [Talosctl CLI][talosctl].
The CLI docs can be found by going to Documentation > Reference > CLI.
For example, [v1.7 CLI][CLI].

## Sample Configuration

import Config from '@site/src/components/Config.js';

<Config data={{
  "type": "talosctl",
  "style": "powerline",
  "powerline_symbol": "\uE0B0",
  "foreground": "#FFFFFF",
  "background": "#FF6C00",
  "template": " \udb84\udcfe {{ .Context }}"
}}/>

## Template ([info][templates])

:::note default template

```template
{{ .Context }}
```

:::

### Properties

| Name         | Type     | Description                           |
| ------------ | -------- | ------------------------------------- |
| `.Context`   | `string` | the current talosctl context           |


[templates]: /docs/configuration/templates
[talosctl]: https://www.talos.dev/
[CLI]: https://www.talos.dev/v1.7/reference/cli/
