---
id: terraform
title: Terraform Context
sidebar_label: Terraform
---

## What

Display the currently active Terraform Workspace name.

## Sample Configuration

import Config from '@site/src/components/Config.js';

<Config data={{
  "type": "terraform",
  "style": "powerline",
  "powerline_symbol": "\uE0B0",
  "foreground": "#000000",
  "background": "#ebcc34",
  "template": "{{.WorkspaceName}}"
}}/>

## Properties

| Name            | Type      | Default | Description                                                                        |
| --------------- | :-------: | :-----: | ---------------------------------------------------------------------------------- |
| `fetch_version` | `boolean` | `false` | fetch the version information from `versions.tf`, `main.tf` or `terraform.tfstate` |

## Template ([info][templates])

:::note default template

```template
{{ .WorkspaceName }}{{ if .Version }} {{ .Version }}{{ end }}
```

:::

### Properties

| Name             | Type     | Description                                       |
| ---------------- | -------- | ------------------------------------------------- |
| `.WorkspaceName` | `string` | is the current workspace name                     |
| `.Version`       | `string` | terraform version (set `fetch_version` to `true`) |

[templates]: /docs/configuration/templates
