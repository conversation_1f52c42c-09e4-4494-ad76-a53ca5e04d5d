---
id: xmake
title: XMake
sidebar_label: XMake
---

## What

Display the currently active [xmake] version.

## Sample Configuration

import Config from "@site/src/components/Config.js";

<Config
  data={{
    type: "xmake",
    style: "powerline",
    powerline_symbol: "\uE0B0",
    foreground: "#e0f2f1",
    background: "#22a079",
    template: " xmake v{{ .Full }} ",
  }}
/>

## Properties

| Name                   |    Type    |   Default   | Description                                                                                                                                                                                                                          |
| ---------------------- | :--------: | :---------: | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `home_enabled`         | `boolean`  |   `false`   | display the segment in the HOME folder or not                                                                                                                                                                                        |
| `fetch_version`        | `boolean`  |   `true`    | fetch the xmake version                                                                                                                                                                                                              |
| `cache_duration`       |  `string`  |    `24h`    | the duration for which the version will be cached. The duration is a string in the format `1h2m3s` and is parsed using the [time.ParseDuration] function from the Go standard library. To disable the cache, use `none`              |
| `missing_command_text` |  `string`  |             | text to display when the command is missing                                                                                                                                                                                          |
| `display_mode`         |  `string`  |  `context`  | <ul><li>`always`: the segment is always displayed</li><li>`files`: the segment is only displayed when file `extensions` listed are present</li><li>`context`: displays the segment when the environment or files is active</li></ul> |
| `version_url_template` |  `string`  |             | a go [text/template][go-text-template] [template][templates] that creates the URL of the version info / release notes                                                                                                                |
| `extensions`           | `[]string` | `xmake.lua` | allows to override the default list of file extensions to validate                                                                                                                                                                   |
| `folders`              | `[]string` |             | allows to override the list of folder names to validate                                                                                                                                                                              |

## Template ([into][templates])

:::note default template

```template
{{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }}
```

:::

### Properties

| Name     | Type     | Description                                        |
| -------- | -------- | -------------------------------------------------- |
| `.Full`  | `string` | the full version                                   |
| `.Major` | `string` | major number                                       |
| `.Minor` | `string` | minor number                                       |
| `.Patch` | `string` | patch number                                       |
| `.Error` | `string` | error encountered when fetching the version string |

[go-text-template]: https://golang.org/pkg/text/template/
[templates]: configuration/templates.mdx
[xmake]: https://xmake.io/
[time.ParseDuration]: https://golang.org/pkg/time/#ParseDuration
