---
id: aws
title: AWS Context
sidebar_label: AWS
---

## What

Display the currently active AWS profile and region.

## Sample Configuration

import Config from "@site/src/components/Config.js";

<Config
  data={{
    type: "aws",
    style: "powerline",
    powerline_symbol: "\uE0B0",
    foreground: "#ffffff",
    background: "#FFA400",
    template: " \uE7AD {{.Profile}}{{if .Region}}@{{.Region}}{{end}}",
  }}
/>

## Properties

| Name              | Type      | Default | Description                                  |
| ----------------- | :-------: | :-----: | -------------------------------------------- |
| `display_default` | `boolean` | `true`  | display the segment when default user or not |

## Template ([info][templates])

:::note default template

```template
{{ .Profile }}{{ if .Region }}@{{ .Region }}{{ end }}
```

:::

### Properties

| Name            | Type     | Description                                 |
| --------------- | -------- | ------------------------------------------- |
| `.Profile`      | `string` | the currently active profile                |
| `.Region`       | `string` | the currently active region                 |
| `.RegionAlias`  | `string` | short alias for the currently active region |

[templates]: /docs/configuration/templates
