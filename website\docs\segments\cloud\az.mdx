---
id: az
title: Azure Subscription
sidebar_label: Azure
---

## What

Display the currently active Azure subscription information.

## Sample Configuration

import Config from "@site/src/components/Config.js";

<Config
  data={{
    type: "az",
    style: "powerline",
    powerline_symbol: "\uE0B0",
    foreground: "#000000",
    background: "#9ec3f0",
    template: " \uebd8 {{ .EnvironmentName }}",
    properties: {
      source: "pwsh",
    },
  }}
/>

## Properties

| Name     |   Type   |          Default           | Description                                                                                                                                                                                                                                                                                      |
| -------- | :------: | :------------------------: | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `source` | `string` | <code>cli&#124;pwsh</code> | sources to get subscription information from. Can be any of the following values, joined by <code>&#124;</code> to loop multiple sources for context. <ul><li>`cli`: fetch the information from the CLI config</li><li>`pwsh`: fetch the information from the PowerShell Module config</li></ul> |

## Template ([info][templates])

:::note default template

```template
{{ .Name }}
```

:::

### Properties

| Name                 | Type      | Description                                                    |
| -------------------- | --------- | -------------------------------------------------------------- |
| `.EnvironmentName`   | `string`  | Azure environment name                                         |
| `.HomeTenantID`      | `string`  | home tenant id                                                 |
| `.ID`                | `string`  | subscription id                                                |
| `.IsDefault`         | `boolean` | is the default subscription or not                             |
| `.Name`              | `string`  | subscription name                                              |
| `.State`             | `string`  | subscription state                                             |
| `.TenantID`          | `string`  | tenant id                                                      |
| `.TenantDisplayName` | `string`  | tenant name                                                    |
| `.User.Name`         | `string`  | user name                                                      |
| `.User.Type`         | `string`  | user type                                                      |
| `.Origin`            | `string`  | where we received the information from, can be `CLI` or `PWSH` |

[templates]: /docs/configuration/templates
[az]: https://www.powershellgallery.com/packages/Az
