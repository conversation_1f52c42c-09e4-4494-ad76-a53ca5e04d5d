---
id: cds
title: CDS (SAP CAP)
sidebar_label: CDS
---

## What

Display the active [CDS CLI][sap-cap-cds] version.

## Sample Configuration

import Config from "@site/src/components/Config.js";

<Config
  data={{
    background: "#a7cae1",
    foreground: "#100e23",
    powerline_symbol: "\ue0b0",
    template: " \ue311 cds {{ .Full }} ",
    style: "powerline",
    type: "cds",
  }}
/>

## Properties

| Name                   |    Type    |                  Default                  | Description                                                                                                                                                                                                                          |
| ---------------------- | :--------: | :---------------------------------------: | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `home_enabled`         | `boolean`  |                  `false`                  | display the segment in the HOME folder or not                                                                                                                                                                                        |
| `fetch_version`        | `boolean`  |                  `true`                   | fetch the CDS version                                                                                                                                                                                                                |
| `cache_duration`       |  `string`  |                   `24h`                   | the duration for which the version will be cached. The duration is a string in the format `1h2m3s` and is parsed using the [time.ParseDuration] function from the Go standard library. To disable the cache, use `none`              |
| `missing_command_text` |  `string`  |                   `""`                    | text to display when the cds command is missing                                                                                                                                                                                      |
| `display_mode`         |  `string`  |                 `context`                 | <ul><li>`always`: the segment is always displayed</li><li>`files`: the segment is only displayed when file `extensions` listed are present</li><li>`context`: displays the segment when the environment or files is active</li></ul> |
| `version_url_template` |  `string`  |                                           | a go [text/template][go-text-template] [template][templates] that creates the URL of the version info / release notes                                                                                                                |
| `extensions`           | `[]string` | `.cdsrc.json, .cdsrc-private.json, *.cds` | allows to override the default list of file extensions to validate                                                                                                                                                                   |
| `folders`              | `[]string` |                                           | allows to override the list of folder names to validate                                                                                                                                                                              |

## Template ([info][templates])

:::note default template

```template
{{ if .Error }}{{ .Error }}{{ else }}{{ .Full }}{{ end }}
```

:::

### Properties

| Name             | Type     | Description                                        |
| ---------------- | -------- | -------------------------------------------------- |
| `.Full`          | `string` | the full version                                   |
| `.Major`         | `string` | major number                                       |
| `.Minor`         | `string` | minor number                                       |
| `.Patch`         | `string` | patch number                                       |
| `.Error`         | `string` | error encountered when fetching the version string |
| `.HasDependency` | `bool`   | a flag if `@sap/cds` was found in `package.json`   |

[go-text-template]: https://golang.org/pkg/text/template/
[templates]: configuration/templates.mdx
[sap-cap-cds]: https://cap.cloud.sap/docs/tools/#command-line-interface-cli
[time.ParseDuration]: https://golang.org/pkg/time/#ParseDuration
