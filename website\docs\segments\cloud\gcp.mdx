---
id: gcp
title: GCP Context
sidebar_label: GCP
---

## What

Display the currently active GCP project, region and account

## Sample Configuration

import Config from "@site/src/components/Config.js";

<Config
  data={{
    type: "gcp",
    style: "powerline",
    powerline_symbol: "\uE0B0",
    foreground: "#ffffff",
    background: "#47888d",
    template: " \uE7B2 {{.Project}} :: {{.Account}} ",
  }}
/>

## Template ([info][templates])

:::note default template

```template
{{ if .Error }}{{ .Error }}{{ else }}{{ .Project }}{{ end }}
```

:::

### Properties

| Name            | Type     | Description                                                              |
| --------------- | -------- | ------------------------------------------------------------------------ |
| `.Project`      | `string` | the currently active project                                             |
| `.Account`      | `string` | the currently active account                                             |
| `.Region`       | `string` | default region for the active context                                    |
| `.ActiveConfig` | `string` | the active configuration name                                            |
| `.Error`        | `string` | contains any error messages generated when trying to load the GCP config |

[templates]: /docs/configuration/templates
