---
id: sitecore
title: Sitecore
sidebar_label: Sitecore
---

import Config from "@site/src/components/Config.js";

## What

Display current [Sitecore] environment. Will not be active when sitecore.json and user.json don't exist.

## Sample Configuration

<Config
  data={{
    type: "sitecore",
    style: "plain",
    foreground: "#000000",
    background: "#FFFFFF",
    template:
      "Env: {{ .EndpointName }}{{ if .CmHost }} CM: {{ .CmHost }}{{ end }}",
  }}
/>

## Properties

| Name              |   Type    | Default | Description                                                                     |
| ----------------- | :-------: | :-----: | ------------------------------------------------------------------------------- |
| `display_default` | `boolean` | `true`  | display the segment or not when the Sitecore environment name matches `default` |

## Template ([info][templates])

:::note default template

```template
{{ .EndpointName }} {{ if .CmHost }}({{ .CmHost }}){{ end }}
```

:::

## Properties

| Name           | Type     | Description                              |
| -------------- | -------- | ---------------------------------------- |
| `EndpointName` | `string` | name of the current Sitecore environment |
| `CmHost`       | `string` | host of the current Sitecore environment |

[go-text-template]: https://golang.org/pkg/text/template/
[templates]: /docs/configuration/templates
[Sitecore]: https://www.sitecore.com/
