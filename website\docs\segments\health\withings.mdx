---
id: withings
title: Withings
sidebar_label: Withings
---

import WithingsConnect from "/img/withings.svg";

## What

The [Withings][withings] health ecosystem of connected devices & apps is designed to improve daily wellbeing
and long-term health.

## Accessing your Withings data

To allow Oh My Posh access your Withings data you need to grant access to read your public activities.
This will give you an access and a refresh token. Paste the tokens into your Withings segment configuration.

Click the following link to connect with Withings:

<div className="withings">
  <a href="https://account.withings.com/oauth2_user/authorize2?client_id=93675962e88ddfe53f83c0c900558f72174e0ac70ccfb57e48053530c7e6e494&response_type=code&redirect_uri=https://ohmyposh.dev/api/auth&scope=user.activity,user.metrics&state=withings">
    <WithingsConnect className="withings" />
  </a>
</div>

## Sample Configuration

import Config from "@site/src/components/Config.js";

<Config
  data={{
    type: "withings",
    style: "powerline",
    powerline_symbol: "\uE0B0",
    foreground: "#ffffff",
    background: "#000000",
    template: "{{ if gt .Weight 0.0 }} {{ round .Weight 2 }}kg {{ end }}",
    properties: {
      access_token: "****************1",
      refresh_token: "****************",
      http_timeout: 1500,
    },
  }}
/>

## Properties

| Name            |   Type   | Default | Description                                                                                   |
| --------------- | :------: | :-----: | --------------------------------------------------------------------------------------------- |
| `access_token`  | `string` |         | token from Withings login, see login link in section above.                                   |
| `refresh_token` | `string` |         | token from Withings login, see login link in section above.                                   |
| `expires_in`    |  `int`   |   `0`   | the default timeout of the token from the Withings login                                      |
| `http_timeout`  |  `int`   |  `500`  | how long do you want to wait before you want to see your prompt more than your Withings data? |

## Template ([info][templates])

:::note default template

```template
{{ if gt .Weight 0.0 }} {{ round .Weight 2 }}kg {{ end }}
```

:::

### Properties

The properties below are available for use in your template

| Name          | Type     | Description                         |
| ------------- | -------- | ----------------------------------- |
| `.Weight`     | `float`  | your last measured weight           |
| `.SleepHours` | `string` | your last measured sleep SleepHours |
| `.Steps`      | `int`    | your last measured steps            |

Now, go out and be active!

[templates]: /docs/configuration/templates
[withings]: https://www.withings.com/
