---
id: dotnet
title: Dotnet
sidebar_label: Dotnet
---

## What

Display the currently active [.NET SDK][net-sdk-docs] version.

## Sample Configuration

import Config from "@site/src/components/Config.js";

<Config
  data={{
    type: "dotnet",
    style: "powerline",
    powerline_symbol: "\uE0B0",
    foreground: "#000000",
    background: "#00ffff",
    template: " \uE77F {{ .Full }} ",
  }}
/>

## Properties

| Name                   |    Type    |                                          Default                                           | Description                                                                                                                                                                                                                          |
| ---------------------- | :--------: | :----------------------------------------------------------------------------------------: | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `home_enabled`         | `boolean`  |                                          `false`                                           | display the segment in the HOME folder or not                                                                                                                                                                                        |
| `fetch_version`        | `boolean`  |                                           `true`                                           | fetch the active version or not; useful if all you need is an icon indicating `dotnet`                                                                                                                                               |
| `cache_duration`       |  `string`  |                                           `none`                                           | the duration for which the version will be cached. The duration is a string in the format `1h2m3s` and is parsed using the [time.ParseDuration] function from the Go standard library. To disable the cache, use `none`              |
| `missing_command_text` |  `string`  |                                                                                            | text to display when the command is missing                                                                                                                                                                                          |
| `display_mode`         |  `string`  |                                         `context`                                          | <ul><li>`always`: the segment is always displayed</li><li>`files`: the segment is only displayed when file `extensions` listed are present</li><li>`context`: displays the segment when the environment or files is active</li></ul> |
| `version_url_template` |  `string`  |                                                                                            | a go [text/template][go-text-template] [template][templates] that creates the URL of the version info / release notes                                                                                                                |
| `extensions`           | `[]string` | `*.cs, *.csx, *.vb, *.fs, *.fsx, *.sln, *.slnf, *.csproj, *.fsproj, *.vbproj, global.json` | allows to override the default list of file extensions to validate                                                                                                                                                                   |
| `folders`              | `[]string` |                                                                                            | allows to override the list of folder names to validate                                                                                                                                                                              |
| `fetch_sdk_version`    | `boolean`  |                                          `false`                                           | fetch the SDK version in `global.json` when present                                                                                                                                                                                  |

## Template ([info][templates])

:::note default template

```template
{{ if .Unsupported }}\uf071{{ else }}{{ .Full }}{{ end }}
```

:::

### Properties

| Name             | Type     | Description                                                         |
| ---------------- | -------- | ------------------------------------------------------------------- |
| `.Full`          | `string` | the full version                                                    |
| `.Major`         | `string` | major number                                                        |
| `.Minor`         | `string` | minor number                                                        |
| `.Patch`         | `string` | patch number                                                        |
| `.Prerelease`    | `string` | prerelease info text                                                |
| `.BuildMetadata` | `string` | build metadata                                                      |
| `.URL`           | `string` | URL of the version info / release notes                             |
| `.SDKVersion`    | `string` | the SDK version in `global.json` when `fetch_sdk_version` is `true` |
| `.Error`         | `string` | error encountered when fetching the version string                  |

[go-text-template]: https://golang.org/pkg/text/template/
[templates]: /docs/configuration/templates
[net-sdk-docs]: https://docs.microsoft.com/en-us/dotnet/core/tools
[time.ParseDuration]: https://golang.org/pkg/time/#ParseDuration
