---
id: mojo
title: <PERSON><PERSON>
sidebar_label: Mojo
---

## What

Display the currently active version of [Mojo][mojo] and the name of the [Magic][magic] virtual environment.

## Sample Configuration

import Config from "@site/src/components/Config.js";

<Config
  data={{
    type: "mojo",
    style: "powerline",
    powerline_symbol: "\uE0B0",
    foreground: "#100e23",
    background: "#906cff",
    template: " 🔥 {{ .Full }} ",
  }}
/>

## Properties

| Name                   |    Type    |             Default             | Description                                                                                                                                                                                                                                                                                                             |
| ---------------------- | :--------: | :-----------------------------: | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `home_enabled`         | `boolean`  |             `false`             | display the segment in the HOME folder or not                                                                                                                                                                                                                                                                           |
| `fetch_virtual_env`    | `boolean`  |             `true`              | fetch the name of the virtualenv or not                                                                                                                                                                                                                                                                                 |
| `display_default`      | `boolean`  |             `true`              | show the name of the virtualenv when it's equal to `default` or not                                                                                                                                                                                                                                                     |
| `fetch_version`        | `boolean`  |             `true`              | fetch the Mojo version or not                                                                                                                                                                                                                                                                                           |
| `cache_duration`       |  `string`  |             `none`              | the duration for which the version will be cached. The duration is a string in the format `1h2m3s` and is parsed using the [time.ParseDuration] function from the Go standard library. To disable the cache, use `none`                                                                                                 |
| `missing_command_text` |  `string`  |                                 | text to display when the command is missing                                                                                                                                                                                                                                                                             |
| `display_mode`         |  `string`  |          `environment`          | <ul><li>`always`: the segment is always displayed</li><li>`files`: the segment is only displayed when file `extensions` listed are present</li><li>`environment`: the segment is only displayed when in a virtual environment</li><li>`context`: displays the segment when the environment or files is active</li></ul> |
| `version_url_template` |  `string`  |                                 | a go [text/template][go-text-template] [template][templates] that creates the URL of the version info / release notes                                                                                                                                                                                                   |
| `extensions`           | `[]string` | `*.🔥, *.mojo, mojoproject.toml` | allows to override the default list of file extensions to validate                                                                                                                                                                                                                                                      |
| `folders`              | `[]string` |                                 | allows to override the list of folder names to validate                                                                                                                                                                                                                                                                 |

## Template ([info][templates])

:::note default template

```template
{{ if .Error }}{{ .Error }}{{ else }}{{ if .Venv }}{{ .Venv }} {{ end }}{{ .Full }}{{ end }}
```

:::

### Properties

| Name     | Type     | Description                                        |
| -------- | -------- | -------------------------------------------------- |
| `.Venv`  | `string` | the virtual environment name (if present)          |
| `.Full`  | `string` | the full version                                   |
| `.Major` | `string` | major number                                       |
| `.Minor` | `string` | minor number                                       |
| `.Patch` | `string` | patch number                                       |
| `.Error` | `string` | error encountered when fetching the version string |

[go-text-template]: https://golang.org/pkg/text/template/
[templates]: /docs/configuration/templates
[mojo]: https://docs.modular.com/mojo
[magic]: https://docs.modular.com/magic
[time.ParseDuration]: https://golang.org/pkg/time/#ParseDuration
