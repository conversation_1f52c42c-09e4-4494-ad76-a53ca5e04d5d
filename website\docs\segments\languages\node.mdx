---
id: node
title: Node
sidebar_label: Node
---

## What

Display the currently active [Node.js][node-js] version.

## Sample Configuration

import Config from "@site/src/components/Config.js";

<Config
  data={{
    type: "node",
    style: "powerline",
    powerline_symbol: "\uE0B0",
    foreground: "#ffffff",
    background: "#6CA35E",
    template: " \uE718 {{ .Full }} ",
  }}
/>

## Properties

| Name                    |    Type    |                                   Default                                    | Description                                                                                                                                                                                                                          |
| ----------------------- | :--------: | :--------------------------------------------------------------------------: | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `home_enabled`          | `boolean`  |                                   `false`                                    | display the segment in the HOME folder or not                                                                                                                                                                                        |
| `fetch_version`         | `boolean`  |                                    `true`                                    | fetch the Node.js version                                                                                                                                                                                                            |
| `cache_duration`        |  `string`  |                                    `none`                                    | the duration for which the version will be cached. The duration is a string in the format `1h2m3s` and is parsed using the [time.ParseDuration] function from the Go standard library. To disable the cache, use `none`              |
| `missing_command_text`  |  `string`  |                                                                              | text to display when the command is missing                                                                                                                                                                                          |
| `display_mode`          |  `string`  |                                  `context`                                   | <ul><li>`always`: the segment is always displayed</li><li>`files`: the segment is only displayed when file `extensions` listed are present</li><li>`context`: displays the segment when the environment or files is active</li></ul> |
| `version_url_template`  |  `string`  |                                                                              | a go [text/template][go-text-template] [template][templates] that creates the URL of the version info / release notes                                                                                                                |
| `fetch_package_manager` | `boolean`  |                                   `false`                                    | define if the current project uses PNPM, Yarn, or NPM                                                                                                                                                                                |
| `pnpm_icon`             |  `string`  |                                  `\uF02C1`                                   | the icon/text to display when using PNPM                                                                                                                                                                                             |
| `yarn_icon`             |  `string`  |                                  `\uF011B`                                   | the icon/text to display when using Yarn                                                                                                                                                                                             |
| `npm_icon`              |  `string`  |                                   `\uE71E`                                   | the icon/text to display when using NPM                                                                                                                                                                                              |
| `bun_icon`              |  `string`  |                                   `\ue76f`                                   | the icon/text to display when using Bun                                                                                                                                                                                              |
| `extensions`            | `[]string` | `*.js, *.ts, package.json, .nvmrc, pnpm-workspace.yaml, .pnpmfile.cjs, .vue` | allows to override the default list of file extensions to validate                                                                                                                                                                   |
| `folders`               | `[]string` |                                                                              | allows to override the list of folder names to validate                                                                                                                                                                              |

## Template ([info][templates])

:::note default template

```template
{{ if .PackageManagerIcon }}{{ .PackageManagerIcon }} {{ end }}{{ .Full }}
```

:::

### Properties

| Name                  | Type      | Description                                                                                              |
| --------------------- | --------- | -------------------------------------------------------------------------------------------------------- |
| `.Full`               | `string`  | the full version                                                                                         |
| `.Major`              | `string`  | major number                                                                                             |
| `.Minor`              | `string`  | minor number                                                                                             |
| `.Patch`              | `string`  | patch number                                                                                             |
| `.URL`                | `string`  | URL of the version info / release notes                                                                  |
| `.Error`              | `string`  | error encountered when fetching the version string                                                       |
| `.PackageManagerName` | `string`  | the package manager name (`bun`, `npm`, `yarn` or `pnpm`) when setting `fetch_package_manager` to `true` |
| `.PackageManagerIcon` | `string`  | the PNPM, Yarn, Bun, or NPM icon when setting `fetch_package_manager` to `true`                          |
| `.Mismatch`           | `boolean` | true if the version in `.nvmrc` is not equal to `.Full`                                                  |
| `.Expected`           | `string`  | the expected version set in `.nvmrc`                                                                     |

[go-text-template]: https://golang.org/pkg/text/template/
[templates]: /docs/configuration/templates
[node-js]: https://nodejs.org
[time.ParseDuration]: https://golang.org/pkg/time/#ParseDuration
