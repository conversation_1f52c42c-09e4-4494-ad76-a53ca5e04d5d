---
id: python
title: Python
sidebar_label: Python
---

## What

Display the currently active python version and virtualenv.
Supports conda, virtualenv and pyenv (if python points to pyenv shim).

## Sample Configuration

import Config from "@site/src/components/Config.js";

<Config
  data={{
    type: "python",
    style: "powerline",
    powerline_symbol: "\uE0B0",
    foreground: "#100e23",
    background: "#906cff",
    template: " \uE235 {{ .Full }} ",
  }}
/>

## Properties

| Name                   |    Type    |                  Default                  | Description                                                                                                                                                                                                                                                                                                             |
| ---------------------- | :--------: | :---------------------------------------: | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `home_enabled`         | `boolean`  |                  `false`                  | display the segment in the HOME folder or not                                                                                                                                                                                                                                                                           |
| `fetch_virtual_env`    | `boolean`  |                  `true`                   | fetch the name of the virtualenv or not                                                                                                                                                                                                                                                                                 |
| `display_default`      | `boolean`  |                  `true`                   | show the name of the virtualenv when it's default (`system`, `base`) or not                                                                                                                                                                                                                                             |
| `fetch_version`        | `boolean`  |                  `true`                   | fetch the python version                                                                                                                                                                                                                                                                                                |
| `cache_duration`       |  `string`  |                  `none`                   | the duration for which the version will be cached. The duration is a string in the format `1h2m3s` and is parsed using the [time.ParseDuration] function from the Go standard library. To disable the cache, use `none`                                                                                                 |
| `missing_command_text` |  `string`  |                                           | text to display when the command is missing                                                                                                                                                                                                                                                                             |
| `display_mode`         |  `string`  |               `environment`               | <ul><li>`always`: the segment is always displayed</li><li>`files`: the segment is only displayed when file `extensions` listed are present</li><li>`environment`: the segment is only displayed when in a virtual environment</li><li>`context`: displays the segment when the environment or files is active</li></ul> |
| `version_url_template` |  `string`  |                                           | a go [text/template][go-text-template] [template][templates] that creates the URL of the version info / release notes                                                                                                                                                                                                   |
| `extensions`           | `[]string` | `*.py, *.ipynb, pyproject.toml, venv.bak` | allows to override the default list of file extensions to validate                                                                                                                                                                                                                                                      |
| `folders`              | `[]string` |                                           | allows to override the list of folder names to validate                                                                                                                                                                                                                                                                 |
| `folder_name_fallback` | `boolean`  |                  `true`                   | instead of `default_venv_names` (case sensitive), use the parent folder name as the virtual environment's name or not                                                                                                                                                                                                   |
| `default_venv_names`   | `[]string` |               `.venv, venv`               | allows to override the list of environment's name replaced when `folder_name_fallback` is `true`                                                                                                                                                                                                                        |

## Template ([info][templates])

:::note default template

```template
{{ if .Error }}{{ .Error }}{{ else }}{{ if .Venv }}{{ .Venv }} {{ end }}{{ .Full }}{{ end }}
```

:::

### Properties

| Name     | Type     | Description                                        |
| -------- | -------- | -------------------------------------------------- |
| `.Venv`  | `string` | the virtual environment name (if present)          |
| `.Full`  | `string` | the full version                                   |
| `.Major` | `string` | major number                                       |
| `.Minor` | `string` | minor number                                       |
| `.Patch` | `string` | patch number                                       |
| `.URL`   | `string` | URL of the version info / release notes            |
| `.Error` | `string` | error encountered when fetching the version string |

[go-text-template]: https://golang.org/pkg/text/template/
[templates]: /docs/configuration/templates
[time.ParseDuration]: https://golang.org/pkg/time/#ParseDuration
