---
id: spotify
title: Spotify
sidebar_label: Spotify
---

## What

Show the currently playing song in the [Spotify][spotify] client.

:::caution
Be aware this can make the prompt a tad bit slower as it needs to get a response from the Spotify player.

On _macOS & Linux_, all states are supported (playing/paused/stopped).

On _Windows/WSL_, **only the playing state is supported** (no information when paused/stopped). It supports
fetching information from the native Spotify application and Edge PWA.
:::

## Sample Configuration

import Config from "@site/src/components/Config.js";

<Config
  data={{
    type: "spotify",
    style: "powerline",
    powerline_symbol: "\uE0B0",
    foreground: "#ffffff",
    background: "#1BD760",
    properties: {
      playing_icon: "\uE602 ",
      paused_icon: "\uF8E3 ",
      stopped_icon: "\uF04D ",
    },
  }}
/>

## Properties

| Name           |   Type   |  Default  | Description                    |
| -------------- | :------: | :-------: | ------------------------------ |
| `playing_icon` | `string` | `\uE602 ` | text/icon to show when playing |
| `paused_icon`  | `string` | `\uF8E3 ` | text/icon to show when paused  |
| `stopped_icon` | `string` | `\uF04D`  | text/icon to show when stopped |

## Template ([info][templates])

:::note default template

```template
{{ .Icon }}{{ if ne .Status \"stopped\" }}{{ .Artist }} - {{ .Track }}{{ end }}
```

:::

### Properties

| Name      | Type     | Description                                    |
| --------- | -------- | ---------------------------------------------- |
| `.Status` | `string` | player status (`playing`, `paused`, `stopped`) |
| `.Artist` | `string` | current artist                                 |
| `.Track`  | `string` | current track                                  |
| `.Icon`   | `string` | icon (based on `.Status`)                      |

[templates]: /docs/configuration/templates
[spotify]: https://www.spotify.com
