---
id: fossil
title: Fossil
sidebar_label: Fossil
---

## What

Display [fossil][fossil] information when in a fossil repository.

## Sample Configuration

import Config from '@site/src/components/Config.js';

<Config data={{
  "type": "fossil",
  "style": "powerline",
  "powerline_symbol": "\uE0B0",
  "foreground": "#193549",
  "background": "#ffeb3b"
}}/>

## Properties

| Name              |   Type    | Default | Description                                                                                                                                                                                                       |
| ----------------- | :-------: | :-----: | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `native_fallback` | `boolean` | `false` | when set to `true` and `fossil.exe` is not available when inside a WSL2 shared Windows drive, we will fallback to the native `fossil` executable to fetch data. Not all information can be displayed in this case |

## Template ([info][templates])

:::note default template

```template
 \ue725 {{.Branch}} {{.Status.String}}
```

:::

### Properties

| Name      | Type           | Description                         |
| --------- | -------------- | ----------------------------------- |
| `.Status` | `FossilStatus` | changes in the worktree (see below) |
| `.Branch` | `string`       | current branch                      |

### FossilStatus

| Name            | Type      | Description                                  |
| --------------- | --------- | -------------------------------------------- |
| `.Modified`     | `int`     | number of edited, updated and changed files  |
| `.Deleted`      | `int`     | number of deleted files                      |
| `.Added`        | `int`     | number of added files                        |
| `.Moved`        | `int`     | number of renamed files                      |
| `.Conflicted`   | `int`     | number of conflicting files                  |
| `.Changed`      | `boolean` | if the status contains changes or not        |
| `.HasConflicts` | `boolean` | if the status contains conflicts or not      |
| `.String`       | `string`  | a string representation of the changes above |

Local changes use the following syntax:

| Icon | Description |
| ---- | ----------- |
| `+`  | added       |
| `!`  | conflicted  |
| `-`  | deleted     |
| `~`  | modified    |
| `>`  | moved       |

[fossil]: https://fossil-scm.org
[templates]: /docs/config-templates
[hyperlinks]: /docs/config-templates#custom
