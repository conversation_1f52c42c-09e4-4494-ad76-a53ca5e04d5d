---
id: git
title: Git
sidebar_label: Git
---

## What

Display git information when in a git repository. Also works for subfolders. For maximum compatibility,
make sure your `git` executable is up-to-date (when branch or status information is incorrect for example).

## Sample Configuration

import Config from "@site/src/components/Config.js";

<Config
  data={{
    type: "git",
    style: "powerline",
    powerline_symbol: "\uE0B0",
    foreground: "#193549",
    background: "#ffeb3b",
    background_templates: [
      "{{ if or (.Working.Changed) (.Staging.Changed) }}#FFEB3B{{ end }}",
      "{{ if and (gt .Ahead 0) (gt .Behind 0) }}#FFCC80{{ end }}",
      "{{ if gt .Ahead 0 }}#B388FF{{ end }}",
      "{{ if gt .Behind 0 }}#B388FB{{ end }}",
    ],
    template:
      "{{ .UpstreamIcon }}{{ .HEAD }}{{if .BranchStatus }} {{ .BranchStatus }}{{ end }}{{ if .Working.Changed }} \uF044 {{ .Working.String }}{{ end }}{{ if and (.Working.Changed) (.Staging.Changed) }} |{{ end }}{{ if .Staging.Changed }} \uF046 {{ .Staging.String }}{{ end }}{{ if gt .StashCount 0 }} \uf0c7 {{ .StashCount }}{{ end }}",
    properties: {
      fetch_status: true,
      fetch_upstream_icon: true,
      untracked_modes: {
        "/Users/<USER>/Projects/oh-my-posh/": "no",
      },
      source: "cli",
      mapped_branches: {
        "feat/*": "🚀 ",
        "bug/*": "🐛 ",
      },
    },
  }}
/>

## Properties

### Fetching information

As doing multiple git calls can slow down the prompt experience, we do not fetch information by default.
You can set the following properties to `true` to enable fetching additional information (and populate the template).

| Name                  |        Type         | Default | Description                                                                                                                                                                                                                                                                                                                           |
| --------------------- | :-----------------: | :-----: | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `fetch_status`        |      `boolean`      | `false` | fetch the local changes                                                                                                                                                                                                                                                                                                               |
| `ignore_status`       |     `[]string`      |         | do not fetch status for these repo's. Uses the repo's root folder and same logic as the [exclude_folders][exclude_folders] property                                                                                                                                                                                                   |
| `fetch_upstream_icon` |      `boolean`      | `false` | fetch upstream icon                                                                                                                                                                                                                                                                                                                   |
| `fetch_bare_info`     |      `boolean`      | `false` | fetch bare repo info                                                                                                                                                                                                                                                                                                                  |
| `untracked_modes`     | `map[string]string` |         | map of repo's where to override the default [untracked files mode][untracked]:<ul><li>`no`</li><li>`normal`</li><li>`all`</li></ul>For example `"untracked_modes": { "/Users/<USER>/repos/repo1": "no" }` - defaults to `normal` for all repo's. If you want to override for all repo's, use `*` to set the mode instead of the repo path |
| `ignore_submodules`   | `map[string]string` |         | map of repo's where to change the [--ignore-submodules][submodules] flag (`none`, `untracked`, `dirty` or `all`). For example `"ignore_submodules": { "/Users/<USER>/repos/repo1": "all" }`. If you want to override for all repo's, use `*` to set the mode instead of the repo path                                                     |
| `native_fallback`     |      `boolean`      | `false` | when set to `true` and `git.exe` is not available when inside a WSL2 shared Windows drive, we will fallback to the native `git` executable to fetch data. Not all information can be displayed in this case                                                                                                                           |
| `fetch_user`          |   [`User`](#user)   | `false` | fetch the current configured user for the repository                                                                                                                                                                                                                                                                                  |
| `status_formats`      | `map[string]string` |         | a key, value map allowing to override how individual status items are displayed. For example, `"status_formats": { "Added": "Added: %d" }` will display the added count as `Added: 1` instead of `+1`. See the [Status](#status) section for available overrides.                                                                     |
| `source`              |      `string`       |  `cli`  | <ul><li>`cli`: fetch the information using the git CLI</li><li>`pwsh`: fetch the information from the [posh-git][poshgit] PowerShell Module</li></ul>                                                                                                                                                                                 |
| `mapped_branches`     |      `object`       |         | custom glyph/text for specific branches. You can use `*` at the end as a wildcard character for matching                                                                                                                                                                                                                              |
| `branch_template`     |      `string`       |         | a [template][templates] to format that branch name. You can use `{{ .Branch }}` as reference to the original branch name                                                                                                                                                                                                              |

### Icons

#### Branch

| Name                    |   Type   | Default  | Description                                                      |
| ----------------------- | :------: | :------: | ---------------------------------------------------------------- |
| `branch_icon`           | `string` | `\uE0A0` | the icon to use in front of the git branch name                  |
| `branch_identical_icon` | `string` | `\u2261` | the icon to display when remote and local are identical          |
| `branch_ahead_icon`     | `string` | `\u2191` | the icon to display when the local branch is ahead of its remote |
| `branch_behind_icon`    | `string` | `\u2193` | the icon to display when the local branch is behind its remote   |
| `branch_gone_icon`      | `string` | `\u2262` | the icon to display when there's no remote branch                |

#### HEAD

| Name               |   Type   | Default  | Description                                                      |
| ------------------ | :------: | :------: | ---------------------------------------------------------------- |
| `commit_icon`      | `string` | `\uF417` | icon/text to display before the commit context (detached HEAD)   |
| `tag_icon`         | `string` | `\uF412` | icon/text to display before the tag context                      |
| `rebase_icon`      | `string` | `\uE728` | icon/text to display before the context when in a rebase         |
| `cherry_pick_icon` | `string` | `\uE29B` | icon/text to display before the context when doing a cherry-pick |
| `revert_icon`      | `string` | `\uF0E2` | icon/text to display before the context when doing a revert      |
| `merge_icon`       | `string` | `\uE727` | icon/text to display before the merge context                    |
| `no_commits_icon`  | `string` | `\uF594` | icon/text to display when there are no commits in the repo       |

#### Upstream

| Name                |        Type         | Default  | Description                                                                                                                                                                  |
| ------------------- | :-----------------: | :------: | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `github_icon`       |      `string`       | `\uF408` | icon/text to display when the upstream is Github                                                                                                                             |
| `gitlab_icon`       |      `string`       | `\uF296` | icon/text to display when the upstream is Gitlab                                                                                                                             |
| `bitbucket_icon`    |      `string`       | `\uF171` | icon/text to display when the upstream is Bitbucket                                                                                                                          |
| `azure_devops_icon` |      `string`       | `\uEBE8` | icon/text to display when the upstream is Azure DevOps                                                                                                                       |
| `codecommit_icon`   |      `string`       | `\uF270` | icon/text to display when the upstream is AWS CodeCommit                                                                                                                     |
| `codeberg_icon`     |      `string`       | `\uF330` | icon/text to display when the upstream is Codeberg                                                                                                                           |
| `git_icon`          |      `string`       | `\uE5FB` | icon/text to display when the upstream is not known/mapped                                                                                                                   |
| `upstream_icons`    | `map[string]string` |          | a key, value map representing the remote URL (or a part of that URL) and icon to use in case the upstream URL contains the key. These get precedence over the standard icons |

## Template ([info][templates])

:::note default template

```template
{{ .HEAD }}{{if .BranchStatus }} {{ .BranchStatus }}{{ end }}{{ if .Working.Changed }} \uF044 {{ .Working.String }}{{ end }}{{ if and (.Staging.Changed) (.Working.Changed) }} |{{ end }}{{ if .Staging.Changed }} \uF046 {{ .Staging.String }}{{ end }}
```

:::

### Properties

| Name             | Type      | Description                                                                                                                      |
| ---------------- | --------- | -------------------------------------------------------------------------------------------------------------------------------- |
| `.RepoName`      | `string`  | the repo folder name                                                                                                             |
| `.Working`       | `Status`  | changes in the worktree (see below)                                                                                              |
| `.Staging`       | `Status`  | staged changes in the work tree (see below)                                                                                      |
| `.HEAD`          | `string`  | the current HEAD context (branch/rebase/merge/...)                                                                               |
| `.Ref`           | `string`  | the current HEAD reference (branch/tag/...)                                                                                      |
| `.Behind`        | `int`     | commits behind of upstream                                                                                                       |
| `.Ahead`         | `int`     | commits ahead of upstream                                                                                                        |
| `.BranchStatus`  | `string`  | the current branch context (ahead/behind string representation)                                                                  |
| `.Upstream`      | `string`  | the upstream name (remote)                                                                                                       |
| `.UpstreamGone`  | `boolean` | whether the upstream is gone (no remote)                                                                                         |
| `.UpstreamIcon`  | `string`  | the upst ream icon (based on the icons above)                                                                                    |
| `.UpstreamURL`   | `string`  | the upstream URL for use in [hyperlinks][hyperlinks] in templates: `{{ url .UpstreamIcon .UpstreamURL }}`                        |
| `.StashCount`    | `int`     | the stash count                                                                                                                  |
| `.WorktreeCount` | `int`     | the worktree count                                                                                                               |
| `.IsWorkTree`    | `boolean` | if in a worktree repo or not                                                                                                     |
| `.IsBare`        | `boolean` | if in a bare repo or not, only set when `fetch_bare_info` is set to `true`                                                       |
| `.Dir`           | `string`  | the repository's root directory                                                                                                  |
| `.RelativeDir`   | `string`  | the current directory relative to the root directory                                                                             |
| `.Kraken`        | `string`  | a link to the current HEAD in [GitKraken][kraken-ref] for use in [hyperlinks][hyperlinks] in templates `{{ url .HEAD .Kraken }}` |
| `.Commit`        | `Commit`  | HEAD commit information (see below)                                                                                              |
| `.Detached`      | `boolean` | true when the head is detached                                                                                                   |
| `.Merge`         | `boolean` | true when in a merge                                                                                                             |
| `.Rebase`        | `Rebase`  | contains the relevant information when in a rebase                                                                               |
| `.CherryPick`    | `boolean` | true when in a cherry pick                                                                                                       |
| `.Revert`        | `boolean` | true when in a revert                                                                                                            |
| `.LatestTag`     | `string`  | the latest tag name                                                                                                              |

#### Status

| Name         | Type      | Description                                  |
| ------------ | --------- | -------------------------------------------- |
| `.Unmerged`  | `int`     | number of unmerged changes                   |
| `.Deleted`   | `int`     | number of deleted changes                    |
| `.Added`     | `int`     | number of added changes                      |
| `.Modified`  | `int`     | number of modified changes                   |
| `.Untracked` | `int`     | number of untracked changes                  |
| `.Changed`   | `boolean` | if the status contains changes or not        |
| `.String`    | `string`  | a string representation of the changes above |

Local changes use the following syntax:

| Icon | Description |
| ---- | ----------- |
| `x`  | Unmerged    |
| `-`  | Deleted     |
| `+`  | Added       |
| `~`  | Modified    |
| `?`  | Untracked   |

#### Commit

| Name         | Type        | Description                             |
| ------------ | ----------- | --------------------------------------- |
| `.Author`    | `User`      | the author of the commit (see below)    |
| `.Committer` | `User`      | the committer of the commit (see below) |
| `.Subject`   | `string`    | the commit subject                      |
| `.Timestamp` | `time.Time` | the commit timestamp                    |
| `.Sha`       | `string`    | the commit SHA1                         |
| `.Refs`      | `Refs`      | the commit references                   |

##### User

| Name     | Type     | Description      |
| -------- | -------- | ---------------- |
| `.Name`  | `string` | the user's name  |
| `.Email` | `string` | the user's email |

##### Refs

| Name       | Type       | Description       |
| ---------- | ---------- | ----------------- |
| `.Heads`   | `[]string` | branches          |
| `.Tags`    | `[]string` | commit's tags     |
| `.Remotes` | `[]string` | remote references |

As these are arrays of strings, you can join them using the `join` function:

```template
{{ join ", " .Commit.Refs.Tags }}
```

#### Rebase

| Name       | Type     | Description                      |
| ---------- | -------- | -------------------------------- |
| `.Current` | `int`    | the current rebase step          |
| `.Total`   | `int`    | the total number of rebase steps |
| `.HEAD`    | `string` | the current HEAD                 |
| `.Onto`    | `string` | the branch we're rebasing onto   |

## posh-git

If you want to display the default [posh-git][poshgit] output, **do not** use this segment
but add the following snippet after initializing Oh My Posh in your `$PROFILE`:

```powershell
function Set-PoshGitStatus {
    $global:GitStatus = Get-GitStatus
    $env:POSH_GIT_STRING = Write-GitStatus -Status $global:GitStatus
}
New-Alias -Name 'Set-PoshContext' -Value 'Set-PoshGitStatus' -Scope Global -Force
```

You can then use the `POSH_GIT_STRING` environment variable in a [text segment][text]:

<Config
  data={{
    template:
      "{{ if .Env.POSH_GIT_STRING }} {{ .Env.POSH_GIT_STRING }} {{ end }}",
  }}
/>

[poshgit]: https://github.com/dahlbyk/posh-git
[templates]: /docs/configuration/templates
[hyperlinks]: /docs/configuration/templates#custom
[untracked]: https://git-scm.com/docs/git-status#Documentation/git-status.txt---untracked-filesltmodegt
[submodules]: https://git-scm.com/docs/git-status#Documentation/git-status.txt---ignore-submodulesltwhengt
[kraken-ref]: https://www.gitkraken.com/invite/nQmDPR9D
[text]: /docs/segments/system/text
[exclude_folders]: /docs/configuration/segment#include--exclude-folders
