---
id: mercurial
title: Mercurial
sidebar_label: Mercurial
---

## What

Display Mercurial information when in a Mercurial repository. For maximum compatibility,
make sure your `hg` executable is up-to-date (when branch or status information is incorrect for example).

## Sample Configuration

import Config from "@site/src/components/Config.js";

<Config
  data={{
    type: "mercurial",
    style: "powerline",
    powerline_symbol: "\uE0B0",
    foreground: "#193549",
    background: "#ffeb3b",
    properties: {
      newprop: "\uEFF1",
    },
  }}
/>

## Properties

### Fetching information

As doing Mercurial (hg) calls can slow down the prompt experience, we do not fetch information by default.
You can set `fetch_status` to `true` to enable fetching additional information (and populate the template).

| Name              |        Type         | Default | Description                                                                                                                                                                                                                                                      |
| ----------------- | :-----------------: | :-----: | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `fetch_status`    |      `boolean`      | `false` | fetch the local changes                                                                                                                                                                                                                                          |
| `native_fallback` |      `boolean`      | `false` | when set to `true` and `hg.exe` is not available when inside a WSL2 shared Windows drive, we will fallback to the native `hg` executable to fetch data. Not all information can be displayed in this case                                                      |
| `status_formats`  | `map[string]string` |         | a key, value map allowing to override how individual status items are displayed. For example, `"status_formats": { "Added": "Added: %d" }` will display the added count as `Added: 1` instead of `+1`. See the [Status](#status) section for available overrides |

## Template ([info][templates])

:::note default template

```template
hg {{.Branch}} {{if .LocalCommitNumber}}({{.LocalCommitNumber}}:{{.ChangeSetIDShort}}){{end}}{{range .Bookmarks }} \uf02e {{.}}{{end}}{{range .Tags}} \uf02b {{.}}{{end}}{{if .Working.Changed}} \uf044 {{ .Working.String }}{{ end }}
```

:::

### Properties

| Name                | Type       | Description                                           |
| ------------------- | ---------- | ----------------------------------------------------- |
| `.Working`          | `Status`   | changes in the worktree (see below)                   |
| `.IsTip`            | `boolean`  | Current commit is the tip commit                      |
| `.ChangeSetID`      | `string`   | The current local commit number                       |
| `.ChangeSetIDShort` | `string`   | The current local commit number                       |
| `.Branch`           | `string`   | current branch (releative URL reported by `svn info`) |
| `.Bookmarks`        | `[]string` | the currently checked out revision number             |
| `.Tags`             | `[]string` | the currently checked out revision number             |

### Status

| Name         | Type      | Description                                  |
| ------------ | --------- | -------------------------------------------- |
| `.Untracked` | `int`     | number of files not under version control    |
| `.Modified`  | `int`     | number of modified files                     |
| `.Deleted`   | `int`     | number of deleted files                      |
| `.Added`     | `int`     | number of added files                        |
| `.Changed`   | `boolean` | if the status contains changes or not        |
| `.String`    | `string`  | a string representation of the changes above |

Local changes use the following syntax:

| Icon | Description |
| ---- | ----------- |
| `?`  | Untracked   |
| `~`  | Modified    |
| `-`  | Deleted     |
| `+`  | Added       |

[templates]: /docs/config-templates
