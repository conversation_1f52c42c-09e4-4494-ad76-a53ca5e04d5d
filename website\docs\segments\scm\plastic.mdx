---
id: plastic
title: Plastic SCM
sidebar_label: Plastic SCM
---

## What

Display Plastic SCM information when in a plastic repository. Also works for subfolders.
For maximum compatibility, make sure your `cm` executable is up-to-date
(when branch or status information is incorrect for example).

## Sample Configuration

import Config from "@site/src/components/Config.js";

<Config
  data={{
    type: "plastic",
    style: "powerline",
    powerline_symbol: "\uE0B0",
    foreground: "#193549",
    background: "#ffeb3b",
    background_templates: [
      "{{ if .MergePending }}#006060{{ end }}",
      "{{ if .Changed }}#FF9248{{ end }}",
      "{{ if and .Changed .Behind }}#ff4500{{ end }}",
      "{{ if .Behind }}#B388FF{{ end }}",
    ],
    template:
      "{{ .Selector }}{{ if .Status.Changed }} \uF044 {{ end }}{{ .Status.String }}",
    properties: {
      fetch_status: true,
    },
  }}
/>

## Plastic SCM Icon

If you want to use the icon of Plastic SCM in the segment, then please help me push the icon in this [issue][fa-issue]
by leaving a like!
![icon](https://www.plasticscm.com/images/icon-logo-plasticscm.svg)

## Properties

### Fetching information

As doing multiple `cm` calls can slow down the prompt experience, we do not fetch information by default.
You can set the following property to `true` to enable fetching additional information (and populate the template).

| Name             |        Type         | Default | Description                                                                                                                                                                                                                                                      |
| ---------------- | :-----------------: | :-----: | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `fetch_status`   |      `boolean`      | `false` | fetch the local changes                                                                                                                                                                                                                                          |
| `status_formats` | `map[string]string` |         | a key, value map allowing to override how individual status items are displayed. For example, `"status_formats": { "Added": "Added: %d" }` will display the added count as `Added: 1` instead of `+1`. See the [Status](#status) section for available overrides |

### Icons

#### Branch

| Name              |   Type   | Default  | Description                                                                                                              |
| ----------------- | :------: | :------: | ------------------------------------------------------------------------------------------------------------------------ |
| `branch_icon`     | `string` | `\uE0A0` | the icon to use in front of the git branch name                                                                          |
| `mapped_branches` | `object` |          | custom glyph/text for specific branches. You can use `*` at the end as a wildcard character for matching                 |
| `branch_template` | `string` |          | a [template][templates] to format that branch name. You can use `{{ .Branch }}` as reference to the original branch name |

#### Selector

| Name          |   Type   | Default  | Description                                                    |
| ------------- | :------: | :------: | -------------------------------------------------------------- |
| `commit_icon` | `string` | `\uF417` | icon/text to display before the commit context (detached HEAD) |
| `tag_icon`    | `string` | `\uF412` | icon/text to display before the tag context                    |

## Template ([info][templates])

:::note default template

```template
{{ .Selector }}
```

:::

### Properties

| Name            | Type     | Description                                                                                                                                                        |
| --------------- | -------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `.Selector`     | `string` | the current selector context (branch/changeset/label)                                                                                                              |
| `.Behind`       | `bool`   | the current workspace is behind and changes are incoming                                                                                                           |
| `.Status`       | `Status` | changes in the workspace (see below)                                                                                                                               |
| `.MergePending` | `bool`   | if a merge is pending and needs to be committed (known issue: when no file is left after a _Change/Delete conflict_ merge, the `MergePending` property is not set) |

### Status

| Name        | Type      | Description                                  |
| ----------- | --------- | -------------------------------------------- |
| `.Unmerged` | `int`     | number of unmerged changes                   |
| `.Deleted`  | `int`     | number of deleted changes                    |
| `.Added`    | `int`     | number of added changes                      |
| `.Modified` | `int`     | number of modified changes                   |
| `.Moved`    | `int`     | number of moved changes                      |
| `.Changed`  | `boolean` | if the status contains changes or not        |
| `.String`   | `string`  | a string representation of the changes above |

Local changes use the following syntax:

| Icon | Description |
| ---- | ----------- |
| `x`  | Unmerged    |
| `-`  | Deleted     |
| `+`  | Added       |
| `~`  | Modified    |
| `v`  | Moved       |

[templates]: /docs/configuration/templates
[fa-issue]: https://github.com/FortAwesome/Font-Awesome/issues/18504
