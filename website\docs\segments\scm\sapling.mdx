---
id: sapling
title: Sapling
sidebar_label: Sapling
---

## What

Display [sapling][sapling] information when in a sapling repository.

## Sample Configuration

import Config from "@site/src/components/Config.js";

<Config
  data={{
    type: "sapling",
    style: "powerline",
    powerline_symbol: "\uE0B0",
    foreground: "#193549",
    background: "#4C9642",
    background_templates: ["{{ if .Bookmark }}#4C9642{{ end }}"],
    properties: {
      fetch_status: true,
    },
  }}
/>

## Properties

### Fetching information

| Name              |        Type         | Default | Description                                                                                                                                                                                                                                                      |
| ----------------- | :-----------------: | :-----: | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `fetch_status`    |      `boolean`      | `true`  | fetch the local changes - defaults to                                                                                                                                                                                                                            |
| `native_fallback` |      `boolean`      | `false` | when set to `true` and `sl.exe` is not available when inside a WSL2 shared Windows drive, we will fallback to the native `sl` executable to fetch data. Not all information can be displayed in this case                                                        |
| `status_formats`  | `map[string]string` |         | a key, value map allowing to override how individual status items are displayed. For example, `"status_formats": { "Added": "Added: %d" }` will display the added count as `Added: 1` instead of `+1`. See the [Status](#status) section for available overrides |

## Template ([info][templates])

:::note default template

```template
{{ if .Bookmark }}\uf097 {{ .Bookmark }}*{{ else }}\ue729 {{ .ShortHash }}{{ end }}{{ if .Working.Changed }} \uf044 {{ .Working.String }}{{ end }}
```

:::

### Properties

| Name           | Type      | Description                                          |
| -------------- | --------- | ---------------------------------------------------- |
| `.RepoName`    | `string`  | the repo folder name                                 |
| `.Working`     | `Status`  | changes in the worktree (see below)                  |
| `.Description` | `string`  | the first line of the commit's description           |
| `.Author`      | `string`  | the author of the commit                             |
| `.Hash`        | `string`  | the full hash of the commit                          |
| `.ShortHash`   | `string`  | the short hash of the commit                         |
| `.When`        | `string`  | the commit's relative time indication                |
| `.Bookmark`    | `string`  | the commit's bookmark (if any)                       |
| `.Dir`         | `string`  | the repository's root directory                      |
| `.RelativeDir` | `string`  | the current directory relative to the root directory |
| `.New`         | `boolean` | true when there are no commits in the repo           |

### Status

| Name         | Type      | Description                                  |
| ------------ | --------- | -------------------------------------------- |
| `.Modified`  | `int`     | number of modified changes                   |
| `.Added`     | `int`     | number of added changes                      |
| `.Deleted`   | `int`     | number of removed changes                    |
| `.Untracked` | `boolean` | number of untracked changes                  |
| `.Clean`     | `int`     | number of clean changes                      |
| `.Missing`   | `int`     | number of missing changes                    |
| `.Ignored`   | `boolean` | number of ignored changes                    |
| `.String`    | `string`  | a string representation of the changes above |

Local changes use the following syntax:

| Icon | Description |
| ---- | ----------- |
| `~`  | Modified    |
| `+`  | Added       |
| `-`  | Deleted     |
| `?`  | Untracked   |
| `=`  | Clean       |
| `!`  | Missing     |
| `Ø`  | Ignored     |

[sapling]: https://sapling-scm.com/
[templates]: /docs/configuration/templates
