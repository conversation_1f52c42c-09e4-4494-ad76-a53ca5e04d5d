---
id: svn
title: Subversion
sidebar_label: Subversion
---

## What

Display subversion information when in a subversion repository. Also works for subfolders. For maximum compatibility,
make sure your `svn` executable is up-to-date (when branch or status information is incorrect for example).

## Sample Configuration

import Config from "@site/src/components/Config.js";

<Config
  data={{
    type: "svn",
    style: "powerline",
    powerline_symbol: "\uE0B0",
    foreground: "#193549",
    background: "#ffeb3b",
    properties: {
      fetch_status: true,
    },
  }}
/>

## Properties

### Fetching information

As doing multiple [subversion][svn] calls can slow down the prompt experience, we do not fetch information by default.
You can set the following properties to `true` to enable fetching additional information (and populate the template).

| Name              |        Type         | Default | Description                                                                                                                                                                                                                                                      |
| ----------------- | :-----------------: | :-----: | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `fetch_status`    |      `boolean`      | `false` | fetch the local changes                                                                                                                                                                                                                                          |
| `native_fallback` |      `boolean`      | `false` | when set to `true` and `svn.exe` is not available when inside a WSL2 shared Windows drive, we will fallback to the native `svn` executable to fetch data. Not all information can be displayed in this case                                                      |
| `status_formats`  | `map[string]string` |         | a key, value map allowing to override how individual status items are displayed. For example, `"status_formats": { "Added": "Added: %d" }` will display the added count as `Added: 1` instead of `+1`. See the [Status](#status) section for available overrides |

:::info
The fields `Repo`, `Branch` and `BaseRev` will still work with `fetch_status` set to `false`.
:::

## Template ([info][templates])

:::note default template

```template
 \ue0a0{{.Branch}} r{{.BaseRev}} {{.Working.String}}
```

:::

### Properties

| Name       | Type     | Description                                                |
| ---------- | -------- | ---------------------------------------------------------- |
| `.Working` | `Status` | changes in the worktree (see below)                        |
| `.Branch`  | `string` | current branch (relative URL reported by `svn info`)       |
| `.BaseRev` | `int`    | the currently checked out revision number                  |
| `.Repo`    | `string` | current repository (repos root URL reported by `svn info`) |

### Status

| Name            | Type      | Description                                    |
| --------------- | --------- | ---------------------------------------------- |
| `.Untracked`    | `int`     | number of files not under version control      |
| `.Modified`     | `int`     | number of modified files                       |
| `.Deleted`      | `int`     | number of deleted files                        |
| `.Added`        | `int`     | number of added files                          |
| `.Moved`        | `int`     | number of changed moved files                  |
| `.Conflicted`   | `int`     | number of changed tracked files with conflicts |
| `.Changed`      | `boolean` | if the status contains changes or not          |
| `.HasConflicts` | `boolean` | if the status contains conflicts or not        |
| `.String`       | `string`  | a string representation of the changes above   |

Local changes use the following syntax:

| Icon | Description |
| ---- | ----------- |
| `?`  | Untracked   |
| `~`  | Modified    |
| `-`  | Deleted     |
| `+`  | Added       |
| `>`  | Moved       |
| `!`  | Conflicted  |

[svn]: https://subversion.apache.org
[templates]: /configuration/templates.mdx
