---
id: command
title: Command
sidebar_label: Command
---

## What

:::caution powershell
While powerful, it tends to take a lot of time executing the command on **PowerShell**.
Even with `–noprofile` it's noticeably slower compared to `sh`. It's advised to look at using
[environment variables][env] when using PowerShell.
:::

Command allows you run an arbitrary shell command. Be aware it spawn a new process to fetch the result, meaning
it will not be able to fetch session based context (look at abusing [environment variables][env] for that).
When the command errors or returns an empty string, this segment isn't rendered.

You have the ability to use `||` or `&&` to stitch commands together and achieve complex results. When using `||`
the first command that returns a string will be used (or none when they all fail to produce output that's not an
error). The `&&` functionality will join the output of the commands when successful. If you want to run the command
as is, you can set `interpret` to `false`.

## Sample Configuration

import Config from "@site/src/components/Config.js";

<Config
  data={{
    type: "prompt",
    alignment: "right",
    segments: [
      {
        type: "command",
        style: "plain",
        foreground: "#ffffff",
        properties: {
          shell: "bash",
          command: "git log --pretty=format:%cr -1 || date +%H:%M:%S",
        },
      },
    ],
  }}
/>

## Properties

| Name        |   Type    | Default | Description                                                                      |
| ----------- | :-------: | :-----: | -------------------------------------------------------------------------------- |
| `shell`     | `string`  | `bash`  | the shell in which to run the command in. Uses `shell -c command` under the hood |
| `interpret` | `boolean` | `true`  | interpret the command or run as is                                               |
| `command`   | `string`  |         | the command(s) to run                                                            |
| `script`    | `string`  |         | the path to a script to run                                                      |

## Template ([info][templates])

:::note default template

```template
{{ .Output }}
```

:::

### Properties

| Name      | Type     | Description                          |
| --------- | -------- | ------------------------------------ |
| `.Output` | `string` | the output of the command or script. |

[env]: /docs/configuration/templates#environment-variables
[templates]: /docs/configuration/templates
