---
id: executiontime
title: Execution Time
sidebar_label: Execution Time
---

## What

Displays the execution time of the previously executed command.

## Sample Configuration

import Config from '@site/src/components/Config.js';

<Config data={{
  "type": "executiontime",
  "style": "powerline",
  "powerline_symbol": "\uE0B0",
  "foreground": "#ffffff",
  "background": "#8800dd",
  "template": " <#fefefe>\ueba2</> {{ .FormattedMs }} ",
  "properties": {
    "threshold": 500,
    "style": "austin",
    "always_enabled": true
  }
}}/>

## Properties

| Name             | Type      | Default  | Description                                                               |
| ---------------- | :-------: | :------: | ------------------------------------------------------------------------- |
| `always_enabled` | `boolean` | `false`  | always show the duration                                                  |
| `threshold`      | `number`  | `500`    | minimum duration (milliseconds) required to enable this segment           |
| `style`          | `enum`    | `austin` | one of the available format options                                       |

### Style

Style specifies the format in which the time will be displayed. The table below shows some example times in each option.

| format        | 0.001s         | 2.1s           | 3m2.1s         | 4h3m2.1s         |
| ------------- | -------------- | -------------- | -------------- | ---------------- |
| `austin`      | `1ms`          | `2.1s`         | `3m 2.1s`      | `4h 3m 2.1s`     |
| `roundrock`   | `1ms`          | `2s 100ms`     | `3m 2s 100ms`  | `4h 3m 2s 100ms` |
| `dallas`      | `0.001`        | `2.1`          | `3:2.1`        | `4:3:2.1`        |
| `galveston`   | `00:00:00`     | `00:00:02`     | `00:03:02`     | `04:03:02`       |
| `galvestonms` | `00:00:00:001` | `00:00:02:100` | `00:03:02:100` | `04:03:02:100`   |
| `houston`     | `00:00:00.001` | `00:00:02.1`   | `00:03:02.1`   | `04:03:02.1`     |
| `amarillo`    | `0.001s`       | `2.1s`         | `182.1s`       | `14,582.1s`      |
| `round`       | `1ms`          | `2s`           | `3m 2s`        | `4h 3m`          |
| `lucky7`      | `    1ms`      | ` 2.00s `      | ` 3m  2s`      | ` 4h  3m`        |

## Template ([info][templates])

:::note default template

```template
{{ .FormattedMs }}
```

:::

### Properties

| Name           | Type     | Description                                    |
| -------------- | -------- | ---------------------------------------------- |
| `.Ms`          | `number` | the execution time in milliseconds             |
| `.FormattedMs` | `string` | the formatted value based on the `style` above |

[templates]: /docs/configuration/templates
