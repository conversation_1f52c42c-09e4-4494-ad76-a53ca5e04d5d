---
id: os
title: OS
sidebar_label: OS
---

## What

Display OS specific info - defaults to Icon.

## Sample Configuration

import Config from '@site/src/components/Config.js';

<Config data={{
  "type": "os",
  "style": "plain",
  "foreground": "#26C6DA",
  "background": "#546E7A",
  "template": " {{ if .WSL }}WSL at {{ end }}{{.Icon}}\uE0B1",
  "properties": {
    "macos": "mac"
  }
}}/>

## Properties

| Name                  | Type      | Default  | Description                                              |
| --------------------- | :-------: | :------: | -------------------------------------------------------- |
| `macos`               | `string`  | `\uF179` | the string to use for macOS                              |
| `linux`               | `string`  | `\uF17C` | the icon to use for Linux                                |
| `windows`             | `string`  | `\uE62A` | the icon to use for Windows                              |
| `display_distro_name` | `boolean` | `false`  | display the distro name instead of icon for Linux or WSL |
| `alma`                | `string`  | `\uF31D` | the icon to use for AlmaLinux OS                         |
| `almalinux`           | `string`  | `\uF31D` | the icon to use for AlmaLinux OS                         |
| `almalinux9`          | `string`  | `\uF31D` | the icon to use for AlmaLinux OS 9                       |
| `alpine`              | `string`  | `\uF300` | the icon to use for Alpine Linux                         |
| `android`             | `string`  | `\uF17b` | the icon to use for Android                              |
| `aosc`                | `string`  | `\uF301` | the icon to use for AOSC OS                              |
| `arch`                | `string`  | `\uF303` | the icon to use for Arch Linux                           |
| `centos`              | `string`  | `\uF304` | the icon to use for CentOS                               |
| `coreos`              | `string`  | `\uF305` | the icon to use for CoreOS Container Linux               |
| `debian`              | `string`  | `\uF306` | the icon to use for Debian                               |
| `deepin`              | `string`  | `\uF321` | the icon to use for deepin                               |
| `devuan`              | `string`  | `\uF307` | the icon to use for Devuan GNU+Linux                     |
| `elementary`          | `string`  | `\uF309` | the icon to use for elementary OS                        |
| `endeavouros`         | `string`  | `\uF322` | the icon to use for EndeavourOS                          |
| `fedora`              | `string`  | `\uF30a` | the icon to use for Fedora                               |
| `gentoo`              | `string`  | `\uF30d` | the icon to use for Gentoo Linux                         |
| `mageia`              | `string`  | `\uF310` | the icon to use for Mageia                               |
| `manjaro`             | `string`  | `\uF312` | the icon to use for Manjaro Linux                        |
| `mint`                | `string`  | `\uF30e` | the icon to use for Linux Mint                           |
| `nixos`               | `string`  | `\uF313` | the icon to use for NixOS                                |
| `opensuse`            | `string`  | `\uF314` | the icon to use for openSUSE                             |
| `opensuse-tumbleweed` | `string`  | `\uF314` | the icon to use for openSUSE Tumbleweed                  |
| `raspbian`            | `string`  | `\uF315` | the icon to use for Raspberry Pi OS (Raspbian)           |
| `redhat`              | `string`  | `\uF316` | the icon to use for Red Hat Enterprise Linux (RHEL)      |
| `rocky`               | `string`  | `\uF32B` | the icon to use for Rocky Linux                          |
| `sabayon`             | `string`  | `\uF317` | the icon to use for Sabayon                              |
| `slackware`           | `string`  | `\uF319` | the icon to use for Slackware Linux                      |
| `ubuntu`              | `string`  | `\uF31b` | the icon to use for Ubuntu                               |

## Template ([info][templates])

:::note default template

```template
{{ if .WSL }}WSL at {{ end }}{{.Icon}}
```

:::

### Properties

| Name    | Type     | Description |
| ------- | -------- | ----------- |
| `.Icon` | `string` | the OS icon |

[templates]: /docs/configuration/templates
