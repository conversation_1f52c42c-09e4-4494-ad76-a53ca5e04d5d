---
id: path
title: Path
sidebar_label: Path
---

## What

Display the current path.

## Sample Configuration

import Config from "@site/src/components/Config.js";

<Config
  data={{
    type: "path",
    style: "powerline",
    powerline_symbol: "\uE0B0",
    foreground: "#ffffff",
    background: "#61AFEF",
    properties: {
      style: "folder",
      mapped_locations: {
        "C:\\temp": "\ue799",
      },
    },
  }}
/>

## Properties

| Name                        |    Type    |  Default   | Description                                                                                                      |
| --------------------------- | :--------: | :--------: | ---------------------------------------------------------------------------------------------------------------- |
| `folder_separator_icon`     |  `string`  |    `/`     | the symbol to use as a separator between folders                                                                 |
| `folder_separator_template` |  `string`  |            | the [template][templates] to use as a separator between folders                                                  |
| `home_icon`                 |  `string`  |    `~`     | the icon to display when at `$HOME`                                                                              |
| `folder_icon`               |  `string`  |    `..`    | the icon to use as a folder indication                                                                           |
| `windows_registry_icon`     |  `string`  |  `\uF013`  | the icon to display when in the Windows registry                                                                 |
| `style`                     |   `enum`   | `agnoster` | how to display the current path                                                                                  |
| `mixed_threshold`           |  `number`  |    `4`     | the maximum length of a path segment that will be displayed when using `Mixed`                                   |
| `max_depth`                 |  `number`  |    `1`     | maximum path depth to display before shortening when using `agnoster_short`                                      |
| `max_width`                 |   `any`    |    `0`     | maximum path length to display when using `powerlevel` or `agnoster`, can leverage [templates]                   |
| `hide_root_location`        | `boolean`  |  `false`   | hides the root location if it doesn't fit in the last `max_depth` folders when using `agnoster_short`            |
| `cycle`                     | `[]string` |            | a list of color overrides to cycle through to colorize the individual path folders, e.g. `[ "#ffffff,#111111" ]` |
| `cycle_folder_separator`    | `boolean`  |  `false`   | colorize the `folder_separator_icon` as well when using a cycle                                                  |
| `folder_format`             |  `string`  |    `%s`    | format to use on individual path folders                                                                         |
| `edge_format`               |  `string`  |    `%s`    | format to use on the first and last folder of the path                                                           |
| `left_format`               |  `string`  |    `%s`    | format to use on the first folder of the path - defaults to `edge_format`                                        |
| `right_format`              |  `string`  |    `%s`    | format to use on the last folder of the path - defaults to `edge_format`                                         |
| `gitdir_format`             |  `string`  |            | format to use for a git root directory                                                                           |
| `display_cygpath`           | `boolean`  |  `false`   | display the Cygwin style path using `cygpath -u $PWD`                                                            |
| `display_root`              | `boolean`  |  `false`   | display the root `/` on Unix systems                                                                             |
| `dir_length`                |  `number`  |    `1`     | the length of the directory name to display when using `fish`                                                    |
| `full_length_dirs`          |  `number`  |    `1`     | indicates how many full length directory names should be displayed when using `fish`                             |

## Mapped Locations

Allows you to override a location with an icon/string.
It validates if the current path **starts with the specific elements** and replaces it with the icon/string if there's a match.
To avoid issues with nested overrides, Oh My Posh will sort the list of mapped locations before doing a replacement.

| Name                       |   Type    | Default | Description                                                                              |
| -------------------------- | :-------: | :-----: | ---------------------------------------------------------------------------------------- |
| `mapped_locations_enabled` | `boolean` | `true`  | replace known locations in the path with the replacements before applying the style      |
| `mapped_locations`         | `object`  |         | custom glyph/text for specific paths. Works regardless of the `mapped_locations_enabled` |

setting.

For example, to swap out `C:\Users\<USER>\GitHub` with a GitHub icon, you can do the following:

<Config
  data={{
    mapped_locations: {
      "C:\\Users\\<USER>\\GitHub": "\uF09B",
    },
  }}
/>

### How it works

- To make mapped locations work cross-platform, use `/` as the path separator, Oh My Posh will
  automatically match effective separators based on the running operating system.
- If you want to match all child directories, you can use `*` as a wildcard, for example:
  `"C:/Users/<USER>/*": "$"` will turn `C:/Users/<USER>/Downloads` into `$/Downloads` but leave `C:/Users/<USER>
- The character `~` at the start of a mapped location will match the user's home directory.
- The match is _case-insensitive on Windows and macOS_, but case-sensitive on other operating systems. This means that for
  user Bill, who has a user account `Bill` on Windows and `bill` on Linux, `~/Foo` might match
  `C:\Users\<USER>\Foo` or `C:\Users\<USER>\foo` on Windows but only `/home/<USER>/Foo` on Linux.

:::warning
To prevent mangling path elements, if you use any text style tags (e.g., `<lightGreen>...</>`) in replacement values,
you should avoid using a chevron character (`<`/`>`) in the `folder_separator_icon` property, and vice versa.
:::

### Using regular expressions

For more complicated cases, you can use the `re:` prefix to use a regular expression with a capture group for matching.
This uses Golang's [regexp] package, so you can use any of the [supported syntax][regexp]. The replacement value will be the first capture group,
subsequent groups will be ignored.

For example, `"re:(C:/[0-9]+/Foo)": "#"` will match `C:\123\Foo\Bar` and replace it with `#\Bar`. The path used for matching
will always use `/`, regardless of the operating system, allowing cross platform matching.

Same as for standard replacements, the match is case insensitive on Windows and WSL mounted drives, but case-sensitive on
other operating systems.

## Style

Style sets the way the path is displayed. Based on previous experience and popular themes, there are 10 flavors.

- `agnoster`
- `agnoster_full`
- `agnoster_short`
- `agnoster_left`
- `full`
- `folder`
- `mixed`
- `letter`
- `unique`
- `powerlevel`
- `fish`

### Agnoster

Renders each intermediate folder as the `folder_icon` separated by the `folder_separator_icon`.
The first and the last (current) folder name are always displayed as-is.

### Agnoster Full

Renders each folder name separated by the `folder_separator_icon`.

### Agnoster Short

When more than `max_depth` levels deep, it renders one `folder_icon` (if `hide_root_location` is `false`,
which means the root folder does not count for depth) followed by the names of the last `max_depth` folders,
separated by the `folder_separator_icon`.

### Agnoster Left

Renders each folder as the `folder_icon` separated by the `folder_separator_icon`.
Only the first folder name and its child are displayed in full.

### Full

Display the current working directory as a full string with each folder separated by the `folder_separator_icon`.

### Folder

Display the name of the current folder.

### Mixed

Works like `agnoster`, but for any intermediate folder name that is short enough, it will be displayed as-is.
The maximum length for the folders to display is governed by the `mixed_threshold` property.

### Letter

Works like `agnoster_full`, but will write every folder name using the first letter only, except when the folder name
starts with a symbol or icon. Specially, the last (current) folder name is always displayed in full.

- `folder` will be shortened to `f`
- `.config` will be shortened to `.c`
- `__pycache__` will be shortened to `__p`
- `➼ folder` will be shortened to `➼ f`

### Unique

Works like `letter`, but will make sure every folder name is the shortest unique value.

The uniqueness refers to the displayed path, so `C:\dev\dev\dev\development` will be displayed as
`C\d\de\dev\development` (instead of `C\d\d\d\development` for `Letter`). Uniqueness does **not** refer to other
folders at the same level, so if `C:\projectA\dev` and `C:\projectB\dev` exist, then both will be displayed as
`C\p\dev`.

### Powerlevel

Works like `unique`, but will stop shortening when `max_width` is reached.

### Fish

Works like `letter`, but will display the first `dir_length` characters of each folder name, except for the last
number of folders specified by `full_length_dirs`, which will be displayed in full. Inspired by the Fish shell PWD.

## Template ([info][templates])

:::note default template

```template
{{ .Path }}
```

:::

### Properties

| Name          | Type       | Description                                                                                                                                                            |
| ------------- | ---------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `.Path`       | `string`   | the current directory (based on the `style` property)                                                                                                                  |
| `.Parent`     | `string`   | the current directory's parent folder which ends with a path separator (designed for use with style `folder`, it is empty if `.Path` contains only one single element) |
| `.RootDir`    | `boolean`  | true if we're at the root directory (no parent)                                                                                                                        |
| `.Location`   | `string`   | the current directory (raw value)                                                                                                                                      |
| `.StackCount` | `int`      | the stack count                                                                                                                                                        |
| `.Writable`   | `boolean`  | is the current directory writable by the user or not                                                                                                                   |
| `.Format`     | `function` | format any path based on the segment's settings (e.g. `{{ .Format .Segments.Git.RelativeDir }}`)                                                                       |

[templates]: /docs/configuration/templates
[regexp]: https://pkg.go.dev/regexp/syntax
