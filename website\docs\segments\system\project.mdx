---
id: project
title: Project
sidebar_label: Project
---

## What

Display the current version of your project defined in the package file.

Supports:

- Node.js project (`package.json`)
- Cargo project (`Cargo.toml`)
- Python project (`pyproject.toml`, supports metadata defined according to [PEP 621][pep621-standard] or [Poetry][poetry-standard])
- Mojo project (`mojoproject.toml`)
- PHP project (`composer.json`)
- Dart project (`pubspec.yaml`)
- Any nuspec based project (`*.nuspec`, first file match info is displayed)
- .NET project (`*.sln`, `*.slnf`, `*.csproj`, `*.vbproj` or `*.fsproj`, first file match info is displayed)
- Julia project (`JuliaProject.toml`, `Project.toml`)
- PowerShell project (`*.psd1`, first file match info is displayed)

## Sample Configuration

import Config from "@site/src/components/Config.js";

<Config
  data={{
    type: "project",
    style: "powerline",
    powerline_symbol: "\uE0B0",
    foreground: "#193549",
    background: "#ffeb3b",
    template:
      " {{ if .Error }}{{ .Error }}{{ else }}{{ if .Version }}\uf487 {{.Version}}{{ end }} {{ if .Name }}{{ .Name }}{{ end }}{{ end }} ",
  }}
/>

## Properties

| Name             |   Type    | Default | Description                                                                                                         |
| ---------------- | :-------: | :-----: | ------------------------------------------------------------------------------------------------------------------- |
| `always_enabled` | `boolean` | `false` | always show the segment                                                                                             |
| `<type>_files`   |  `array`  |  `[]`   | override the project's files to validate for. Use the `.Type` values listed below to override (e.g. `dotnet_files`) |

## Template ([info][templates])

:::note default template

```template
 {{ if .Error }}{{ .Error }}{{ else }}{{ if .Version }}\uf487 {{.Version}}{{ end }} {{ if .Name }}{{ .Name }}{{ end }}{{ end }}
```

:::

### Properties

| Name       | Type     | Description                                                                                                                                                                                      |
| ---------- | -------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `.Type`    | `string` | The type of project:<ul><li>`node`</li><li>`cargo`</li><li>`python`</li><li>`mojo`</li><li>`php`</li><li>`dart`</li><li>`nuspec`</li><li>`dotnet`</li><li>`julia`</li><li>`powershell`</li></ul> |
| `.Version` | `string` | The version of your project                                                                                                                                                                      |
| `.Target`  | `string` | The target framework/language version of your project                                                                                                                                            |
| `.Name`    | `string` | The name of your project                                                                                                                                                                         |
| `.Error`   | `string` | The error context when we can't fetch the project info                                                                                                                                           |

[templates]: /docs/configuration/templates
[pep621-standard]: https://peps.python.org/pep-0621/
[poetry-standard]: https://python-poetry.org/docs/pyproject/
