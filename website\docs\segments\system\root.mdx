---
id: root
title: Root
sidebar_label: Root
---

## What

Show when the current user is root or when in an elevated shell (Windows).

## Sample Configuration

import Config from '@site/src/components/Config.js';

<Config data={{
  "type": "root",
  "style": "powerline",
  "powerline_symbol": "\uE0B0",
  "foreground": "#111111",
  "background": "#ffff66",
  "template": "\uF0E7"
}}/>

## Template ([info][templates])

:::note default template

``` template
\uF0E7
```

:::

[templates]: /docs/configuration/templates
