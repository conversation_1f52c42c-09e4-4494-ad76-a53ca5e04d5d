---
id: shell
title: Shell
sidebar_label: Shell
---

## What

Show the current shell name (zsh, PowerShell, bash, ...).

## Sample Configuration

import Config from '@site/src/components/Config.js';

<Config data={{
  "type": "shell",
  "style": "powerline",
  "powerline_symbol": "\uE0B0",
  "foreground": "#ffffff",
  "background": "#0077c2",
  "properties": {
    "mapped_shell_names": {
      "pwsh": "PS"
    }
  }
}}/>

## Properties

| Name                 | Type     | Default | Description                                                                   |
| -------------------- | :------: | :-----: | ----------------------------------------------------------------------------- |
| `mapped_shell_names` | `object` |         | custom glyph/text to use in place of specified shell names (case-insensitive) |

## Template ([info][templates])

:::note default template

```template
{{ .Name }}
```

:::

### Properties

| Name       | Type     | Description       |
| ---------- | -------- | ----------------- |
| `.Name`    | `string` | the shell name    |
| `.Version` | `string` | the shell version |

[templates]: /docs/configuration/templates
