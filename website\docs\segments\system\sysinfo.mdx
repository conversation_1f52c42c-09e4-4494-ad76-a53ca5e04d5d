---
id: sysinfo
title: System Info
sidebar_label: System Info
---

## SysInfo

Display SysInfo.

## Sample Configuration

import Config from '@site/src/components/Config.js';

<Config data={{
  "type":"sysinfo",
  "powerline_symbol": "\ue0b0",
  "foreground": "#ffffff",
  "background": "#8f43f3",
  "template":" \ue266 {{ round .PhysicalPercentUsed .Precision }}% ",
  "properties": {
    "precision": 2
  },
  "style":"powerline"
}}/>

## Properties

| Name        | Type  | Default | Description                             |
| ----------- | :---: | :-----: | --------------------------------------- |
| `Precision` | `int` | `2`     | The precision used for any float values |

## Template ([info][templates])

:::note default template

```template
{{ round .PhysicalPercentUsed .Precision }}
```

:::

### Properties

| Name                       | Type       | Description                                                                                                               |
| -------------------------- | ---------- | ------------------------------------------------------------------------------------------------------------------------- |
| `.PhysicalTotalMemory`     | `int`      | is the total of used physical memory                                                                                      |
| `.PhysicalAvailableMemory` | `int`      | is the total available physical memory (i.e. the amount immediately available to processes)                               |
| `.PhysicalFreeMemory`      | `int`      | is the total of free physical memory (i.e. considers memory used by the system for any reason [e.g. caching] as occupied) |
| `.PhysicalPercentUsed`     | `float64`  | is the percentage of physical memory in usage                                                                             |
| `.SwapTotalMemory`         | `int`      | is the total of used swap memory                                                                                          |
| `.SwapFreeMemory`          | `int`      | is the total of free swap memory                                                                                          |
| `.SwapPercentUsed`         | `float64`  | is the percentage of swap memory in usage                                                                                 |
| `.Load1`                   | `float64`  | is the current load1 (can be empty on windows)                                                                            |
| `.Load5`                   | `float64`  | is the current load5 (can be empty on windows)                                                                            |
| `.Load15`                  | `float64`  | is the current load15 (can be empty on windows)                                                                           |
| `.Disks`                   | `[]struct` | an array of [IOCountersStat][ioinfo] object, you can use any property it has e.g. `.Disks.disk0.IoTime`                   |

[cpuinfo]: https://github.com/shirou/gopsutil/blob/78065a7ce2021f6a78c8d6f586a2683ba501dcec/cpu/cpu.go#L32
[ioinfo]: https://github.com/shirou/gopsutil/blob/e0ec1b9cda4470db704a862282a396986d7e930c/disk/disk.go#L32
[templates]: /docs/configuration/templates
