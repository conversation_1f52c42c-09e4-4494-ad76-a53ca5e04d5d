---
id: text
title: Text
sidebar_label: Text
---

## What

Display text.

## Sample Configuration

import Config from '@site/src/components/Config.js';

<Config data={{
  "type": "text",
  "style": "plain",
  "foreground": "#E06C75",
  "template": "\u276F"
}}/>

## Template ([info][templates])

### Properties

Text segments have no special properties. See ([info][templates]) for globally available properties.

[coloring]: /docs/configuration/colors
[templates]: /docs/configuration/templates
