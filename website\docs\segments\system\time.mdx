---
id: time
title: Time
sidebar_label: Time
---

## What

Show the current timestamp.

## Sample Configuration

import Config from '@site/src/components/Config.js';

<Config data={{
  "type": "time",
  "style": "plain",
  "foreground": "#007ACC",
  "properties": {
    "time_format": "15:04:05"
  }
}}/>

## Properties

| Name          | Type     | Default    | Description   |
| ------------- | :------: | :--------: | ------------- |
| `time_format` | `string` | `15:04:05` | Format to use |

## Template ([info][templates])

:::note default template

```template
{{ .CurrentDate | date .Format }}
```

:::

### Properties

| Name           | Type     | Description                             |
| -------------- | -------- | --------------------------------------- |
| `.Format`      | `string` | The time format (set via `time_format`) |
| `.CurrentDate` | `time`   | The time to display (testing purpose)   |

## Syntax

### Formats

Follows the [golang datetime standard][format]:

| DateTime                                         | Format                                                  |
| ------------------------------------------------ | ------------------------------------------------------- |
| **Year**                                         | `06`, `2006`                                            |
| **Month**                                        | `01`, `1`, `Jan`, `January`                             |
| **Day**                                          | `02`, `2`, `_2` <sub>(width two, right justified)</sub> |
| **Weekday**                                      | `Mon`, `Monday`                                         |
| **Hours**                                        | `03`, `3`, `15`                                         |
| **Minutes**                                      | `04`, `4`                                               |
| **Seconds**                                      | `05`, `5`                                               |
| **ms μs ns**                                     | `.000`, `.000000`, `.000000000`                         |
| **ms μs ns** <sub>(trailing zeros removed)</sub> | `.999`, `.999999`, `.999999999`                         |
| **am/pm**                                        | `PM`, `pm`                                              |
| **Timezone**                                     | `MST`                                                   |
| **Offset**                                       | `-0700`, `-07`, `-07:00`, `Z0700`, `Z07:00`             |



### Predefined formats

The following predefined date and timestamp [format constants][format-constants] are also available:

| Name            | Format                                |
| --------------- | ------------------------------------- |
| **ANSIC**       | `Mon Jan _2 15:04:05 2006`            |
| **UnixDate**    | `Mon Jan _2 15:04:05 MST 2006`        |
| **RubyDate**    | `Mon Jan 02 15:04:05 -0700 2006`      |
| **RFC822**      | `02 Jan 06 15:04 MST`                 |
| **RFC822Z**     | `02 Jan 06 15:04 -0700`               |
| **RFC850**      | `Monday, 02-Jan-06 15:04:05 MST`      |
| **RFC1123**     | `Mon, 02 Jan 2006 15:04:05 MST`       |
| **RFC1123Z**    | `Mon, 02 Jan 2006 15:04:05 -0700`     |
| **RFC3339**     | `2006-01-02T15:04:05Z07:00`           |
| **RFC3339Nano** | `2006-01-02T15:04:05.999999999Z07:00` |
| **Kitchen**     | `3:04PM`                              |
| **Stamp**       | `Jan _2 15:04:05`                     |
| **StampMilli**  | `Jan _2 15:04:05.000`                 |
| **StampMicro**  | `Jan _2 15:04:05.000000`              |
| **StampNano**   | `Jan _2 15:04:05.000000000`           |


## Examples

To display the time in multiple time zones, using [Sprig's Date Functions][sprig-date]:

```text
{{ .CurrentDate | date .Format }} {{ dateInZone "15:04Z" .CurrentDate "UTC" }}
```

[templates]: /docs/configuration/templates
[format]: https://yourbasic.org/golang/format-parse-string-time-date-example/
[format-constants]: https://golang.org/pkg/time/#pkg-constants
[sprig-date]: https://masterminds.github.io/sprig/date.html
