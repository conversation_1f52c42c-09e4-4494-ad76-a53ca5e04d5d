---
id: upgrade
title: Upgrade notice
sidebar_label: Upgrade
---

## What

Displays when there's an update available for Oh My Posh.

## Sample Configuration

import Config from "@site/src/components/Config.js";

<Config
  data={{
    type: "upgrade",
    style: "plain",
    foreground: "#111111",
    background: "#FFD664",
    properties: {
      cache_duration: "168h",
    },
  }}
/>

## Properties

| Name             |   Type   | Default | Description                                                                                                                                                                                                             |
| ---------------- | :------: | :-----: | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `cache_duration` | `string` | `168h`  | the duration for which the segment will be cached. The duration is a string in the format `1h2m3s` and is parsed using the [time.ParseDuration] function from the Go standard library. To disable the cache, use `none` |

## Template ([info][templates])

:::note default template

```template
 \uf019
```

:::

### Properties

| Name       | Type     | Description                         |
| ---------- | -------- | ----------------------------------- |
| `.Current` | `string` | the current version number          |
| `.Latest`  | `string` | the latest available version number |

[templates]: /docs/configuration/templates
[time.ParseDuration]: https://golang.org/pkg/time/#ParseDuration
