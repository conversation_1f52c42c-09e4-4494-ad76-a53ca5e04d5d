---
id: ipify
title: Ipify
sidebar_label: Ipify
---

## What

[Ipify][ipify] is a simple Public IP Address API, it returns your public IP Address in plain text.

## Sample Configuration

import Config from "@site/src/components/Config.js";

<Config
  data={{
    type: "ipify",
    style: "diamond",
    foreground: "#ffffff",
    background: "#c386f1",
    leading_diamond: "\ue0b6",
    trailing_diamond: "\uE0B0",
    template: "{{ .IP }}",
    properties: {
      http_timeout: 1000,
    },
  }}
/>

## Properties

| Name           |   Type   |         Default         | Description                                                                     |
| -------------- | :------: | :---------------------: | ------------------------------------------------------------------------------- |
| `url`          | `string` | `https://api.ipify.org` | The Ipify URL, by default IPv4 is used, use `https://api64.ipify.org` for IPv6  |
| `http_timeout` |  `int`   |          `20`           | in milliseconds - how long may the segment wait for a response of the ipify API |

## Template ([info][templates])

:::note default template

```template
{{ .IP }}
```

:::

### Properties

| Name | Type     | Description              |
| ---- | -------- | ------------------------ |
| .IP  | `string` | Your external IP address |

[templates]: /docs/configuration/templates
[ipify]: https://www.ipify.org/
