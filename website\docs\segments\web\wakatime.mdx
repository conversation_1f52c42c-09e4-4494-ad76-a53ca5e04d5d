---
id: wakatime
title: Wakatime
sidebar_label: Wakatime
---

## What

Shows the tracked time on [wakatime][wt] of the current day

:::caution
You **must** request an API key at the [wakatime][wt] website.
The free tier for is sufficient. You'll find the API key in your profile settings page.
:::

## Sample Configuration

import Config from "@site/src/components/Config.js";

<Config
  data={{
    type: "wakatime",
    style: "powerline",
    powerline_symbol: "\uE0B0",
    foreground: "#ffffff",
    background: "#007acc",
    properties: {
      url: "https://wakatime.com/api/v1/users/current/summaries?start=today&end=today&api_key=API_KEY",
      http_timeout: 500,
    },
  }}
/>

## Properties

| Name           |   Type   | Default | Description                                                                                                                                    |
| -------------- | :------: | :-----: | ---------------------------------------------------------------------------------------------------------------------------------------------- |
| `url`          | `string` |         | The Wakatime [summaries][wk-summaries] URL, including the API key. Example above.                                                              |
| `http_timeout` |  `int`   |  `20`   | The time (_in milliseconds_, `ms`) it takes to consider an http request as **timed-out**. If no segment is shown, try increasing this timeout. |

### Dynamic API Key

If you don't want to include the API key into your configuration, the following modification can be done.

<Config
  data={{
    properties: {
      url: "https://wakatime.com/api/v1/users/current/summaries?start=today&end=today&api_key={{ .Env.WAKATIME_API_KEY }}",
      http_timeout: 500,
    },
  }}
/>

:::note

`WAKATIME_API_KEY` is an example, **any name is possible and acceptable** as long as the environment variable exists and contains the API key value.

Please refer to the [Environment Variable][templates-environment-variables] page for more information.

:::

## Template ([info][templates])

:::note default template

```template
{{ secondsRound .CumulativeTotal.Seconds }}
```

:::

### Properties

| Name               | Type       | Description                              |
| ------------------ | ---------- | ---------------------------------------- |
| `.CumulativeTotal` | `wtTotals` | object holding total tracked time values |

### wtTotals Properties

| Name       | Type      | Description                                                     |
| ---------- | --------- | --------------------------------------------------------------- |
| `.Seconds` | `float64` | a number representing the total tracked time in seconds         |
| `.Text`    | `string`  | a string with human readable tracked time (eg: "2 hrs 30 mins") |

[wt]: https://wakatime.com
[wk-summaries]: https://wakatime.com/developers#summaries
[templates]: /docs/configuration/templates
[templates-environment-variables]: /docs/configuration/templates#environment-variables
