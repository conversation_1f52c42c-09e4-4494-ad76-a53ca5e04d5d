{"name": "website", "version": "0.0.0", "private": true, "scripts": {"start": "cross-env NODE_ENV=development docusaurus start --poll 1000", "build": "docusaurus build", "serve": "docusaurus serve", "themes": "node export_themes.js", "clear": "docusaurus clear"}, "dependencies": {"@docusaurus/core": "^3.8.0", "@docusaurus/preset-classic": "^3.8.0", "@docusaurus/theme-search-algolia": "^3.8.0", "@iarna/toml": "^2.2.5", "@mdx-js/react": "^3.1.0", "classnames": "^2.5.1", "query-string": "9.2.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.6.1", "yaml": "^2.8.0"}, "devDependencies": {"cross-env": "^7.0.3", "docusaurus-node-polyfills": "^1.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"npm": "10.1.0", "node": "20.9.0"}, "volta": {"node": "20.9.0"}}