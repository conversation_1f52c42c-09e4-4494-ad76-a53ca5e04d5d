/* stylelint-disable docusaurus/copyright-header */
/**
 * Any CSS included here will be global. The classic template
 * bundles Infima by default. Infima is a CSS framework designed to
 * work well for content-centric websites.
 */

/* You can override the default Infima variables here. */
:root {
  --ifm-color-primary: #2c7ae0;
  --ifm-color-primary-dark: rgb(38, 103, 189);
  --ifm-color-primary-darker: rgb(28, 75, 138);
  --ifm-color-primary-darkest: rgb(19, 51, 94);
  --ifm-color-primary-light: rgb(74, 143, 232);
  --ifm-color-primary-lighter: rgb(95, 157, 237);
  --ifm-color-primary-lightest: rgb(8, 9, 10);
  --ifm-code-font-size: 95%;
  --ifm-pre-background: #232136;
}

.docusaurus-highlight-code-line {
  background-color: rgb(72, 77, 91);
  display: block;
  margin: 0 calc(-1 * var(--ifm-pre-padding));
  padding: 0 var(--ifm-pre-padding);
}

.badge {
  display: none !important;
}

.hero {
  background-color: #173448;
}

.hero--image {
  margin-top: 4rem;
}

.header-github-link:hover {
  opacity: 0.6;
}

.header-github-link:before {
  content: "";
  width: 24px;
  height: 24px;
  display: flex;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12'/%3E%3C/svg%3E")
    no-repeat;
}

html[data-theme="dark"] .header-github-link:before {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='white' d='M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12'/%3E%3C/svg%3E")
    no-repeat;
}

.header-bluesky-link {
  padding-top: 0px;
}

.header-bluesky-link:hover {
  opacity: 0.6;
}

.header-bluesky-link:before {
  content: "@";
  color: #4163EB;
  font-style: bold;
  font-size: 24px;
  padding-right: 10px;
}

.header-affiliate-link:hover {
  opacity: 0.6;
}

.header-affiliate-link:before {
  content: "";
  width: 24px;
  height: 24px;
  display: flex;
  background: url("data:image/svg+xml,%3Csvg fill='black' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg' class='css-1hsn8wk'%3E%3Cpath d='M51.0696 4.92194H88.7341C94.8309 4.92194 99.7736 10.0293 99.7736 16.3295V60.6175C99.7736 66.9179 94.8309 72.0252 88.7341 72.0252H34.835L51.0696 4.92194Z' fill='currentColor'%3E%3C/path%3E%3Cpath d='M41.2866 17.6346H10.9455C4.90046 17.6346 0 22.7419 0 29.0421V73.3302C0 79.6305 4.90046 84.7378 10.9455 84.7378H48.2888L49.7863 78.495H26.6878L41.2866 17.6346Z' %3E%3C/path%3E%3C/svg%3E")
    no-repeat;
}

html[data-theme="dark"] .header-affiliate-link:before {
  background: url("data:image/svg+xml,%3Csvg fill='white' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg' class='css-1hsn8wk'%3E%3Cpath d='M51.0696 4.92194H88.7341C94.8309 4.92194 99.7736 10.0293 99.7736 16.3295V60.6175C99.7736 66.9179 94.8309 72.0252 88.7341 72.0252H34.835L51.0696 4.92194Z' %3E%3C/path%3E%3Cpath d='M41.2866 17.6346H10.9455C4.90046 17.6346 0 22.7419 0 29.0421V73.3302C0 79.6305 4.90046 84.7378 10.9455 84.7378H48.2888L49.7863 78.495H26.6878L41.2866 17.6346Z' %3E%3C/path%3E%3C/svg%3E")
    no-repeat;
}

.header-discord-link {
  height: 28px;
}

.header-discord-link:hover {
  opacity: 0.6;
}

.header-discord-link:before {
  content: "";
  width: 24px;
  height: 24px;
  display: flex;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 127.14 96.36' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill%3D%22%235865F2%22 d='M107.7,8.07A105.15,105.15,0,0,0,81.47,0a72.06,72.06,0,0,0-3.36,6.83A97.68,97.68,0,0,0,49,6.83,72.37,72.37,0,0,0,45.64,0,105.89,105.89,0,0,0,19.39,8.09C2.79,32.65-1.71,56.6.54,80.21h0A105.73,105.73,0,0,0,32.71,96.36,77.7,77.7,0,0,0,39.6,85.25a68.42,68.42,0,0,1-10.85-5.18c.91-.66,1.8-1.34,2.66-2a75.57,75.57,0,0,0,64.32,0c.87.71,1.76,1.39,2.66,2a68.68,68.68,0,0,1-10.87,5.19,77,77,0,0,0,6.89,11.1A105.25,105.25,0,0,0,126.6,80.22h0C129.24,52.84,122.09,29.11,107.7,8.07ZM42.45,65.69C36.18,65.69,31,60,31,53s5-12.74,11.43-12.74S54,46,53.89,53,48.84,65.69,42.45,65.69Zm42.24,0C78.41,65.69,73.25,60,73.25,53s5-12.74,11.44-12.74S96.23,46,96.12,53,91.08,65.69,84.69,65.69Z'/%3E%3C/svg%3E")
    no-repeat;
}

html[data-theme="dark"] .header-discord-link:before {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 127.14 96.36' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill%3D%22%235865F2%22 d='M107.7,8.07A105.15,105.15,0,0,0,81.47,0a72.06,72.06,0,0,0-3.36,6.83A97.68,97.68,0,0,0,49,6.83,72.37,72.37,0,0,0,45.64,0,105.89,105.89,0,0,0,19.39,8.09C2.79,32.65-1.71,56.6.54,80.21h0A105.73,105.73,0,0,0,32.71,96.36,77.7,77.7,0,0,0,39.6,85.25a68.42,68.42,0,0,1-10.85-5.18c.91-.66,1.8-1.34,2.66-2a75.57,75.57,0,0,0,64.32,0c.87.71,1.76,1.39,2.66,2a68.68,68.68,0,0,1-10.87,5.19,77,77,0,0,0,6.89,11.1A105.25,105.25,0,0,0,126.6,80.22h0C129.24,52.84,122.09,29.11,107.7,8.07ZM42.45,65.69C36.18,65.69,31,60,31,53s5-12.74,11.43-12.74S54,46,53.89,53,48.84,65.69,42.45,65.69Zm42.24,0C78.41,65.69,73.25,60,73.25,53s5-12.74,11.44-12.74S96.23,46,96.12,53,91.08,65.69,84.69,65.69Z'/%3E%3C/svg%3E")
    no-repeat;
}

/* Admonition */

div.theme-admonition.theme-admonition-tip.alert.alert--success {
  background-color: #9ccfd8;
  border-color: #56949f;
}

html[data-theme="dark"] div.theme-admonition.theme-admonition-tip {
  color: #102445;
}

html[data-theme="dark"] div.theme-admonition.theme-admonition-tip svg {
  fill: #102445;
}

html[data-theme="dark"] div.theme-admonition.theme-admonition-tip a {
  color: #102445;
}

iframe.youtube {
  width: 100%;
  margin-bottom: 15px;
}

@media screen and (max-width: 350px) {
  iframe.youtube {
    height: 200px;
  }
}

@media screen and (min-width: 350px) {
  iframe.youtube {
    height: 300px;
  }
}

@media screen and (min-width: 600px) {
  iframe.youtube {
    height: 400px;
  }
}

.winstore {
  width: 200px;
  border-radius: 8px;
  margin-top: 25px;
}

.withings {
  width: 150px;
}

[data-theme="light"] .withings path {
  fill: white;
}

[data-theme="dark"] .withings path {
  fill: black;
}

[data-theme="dark"] div.withings {
  background: white;
  padding: 17px 10px 10px 10px;
  display: inline;
  border-radius: 10px;
}

[data-theme="light"] div.withings {
  background: black;
  padding: 17px 10px 10px 10px;
  display: inline;
  border-radius: 10px;
}

h1.hero__title {
  color: white;
}

p.hero__subtitle {
  color: white;
}

a.getStarted_Sjon {
  color: white;
}

[data-theme="light"] .themedImage_ToTc {
  filter: invert(0%) sepia(8%) saturate(2885%) hue-rotate(67deg) brightness(84%)
    contrast(87%);
}

[data-theme="dark"] .themedImage_ToTc {
  filter: invert(100%) sepia(100%) saturate(0%) hue-rotate(226deg)
    brightness(102%) contrast(101%);
}

/* Fonts */

@font-face {
  font-family: "Victor Mono";
  src: url('/static/fonts/VictorMono.ttf') format("truetype");
}

:root {
  --ifm-font-family-monospace: "Inter";
  --ifm-font-family-base: "Inter";
}

code {
  font-family: "Victor Mono", monospace;
}

code[class*="language-"],
pre[class*="language-"] {
  font-family: "Victor Mono", monospace;
}

/* Dev settings */

[data-theme="dark"]
  .themedImage_node_modules-\@docusaurus-theme-classic-lib-theme-ThemedImage-styles-module {
  filter: invert(100%) sepia(100%) saturate(0%) hue-rotate(226deg)
    brightness(102%) contrast(101%);
}

[data-theme="light"]
  .themedImage_node_modules-\@docusaurus-theme-classic-lib-theme-ThemedImage-styles-module {
  filter: invert(0%) sepia(8%) saturate(2885%) hue-rotate(67deg) brightness(84%)
    contrast(87%);
}

a.getStarted_src-pages-styles-module {
  color: white;
}

table {
  /* We need this to be able to use border-radius. */
  border-collapse: separate;

  /* Add a 1px border spacing for out box-shadow to fit into. Increase this if you want to increase the border width. */
  border-spacing: 1px;
}

table th,
table td {
  /* Remove any borders from our stylesheet. */
  border: 0;

  /* Use the spread value on the box-shadow to set the border width. */
  box-shadow: 0 0 0 1px #c8c9cc;
}

table th:first-child {
  border-top-left-radius: 6.4px;
}

table th:last-child {
  border-top-right-radius: 6.4px;
}

table tr:last-child td:first-child {
  border-bottom-left-radius: 6.4px;
}

table tr:last-child td:last-child {
  border-bottom-right-radius: 6.4px;
}
